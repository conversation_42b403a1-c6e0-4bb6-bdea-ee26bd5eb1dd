#!/bin/bash

# UniPay Portal 自动部署脚本
# 适用于 CentOS/Ubuntu/Debian 系统
# 作者: UniPay Team
# 版本: v2.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DOMAIN="ybdl.shop"
PROJECT_PATH="/www/wwwroot/unipay-portal"
NGINX_CONF_PATH="/etc/nginx/sites-available/unipay-portal"
NGINX_ENABLED_PATH="/etc/nginx/sites-enabled/unipay-portal"
BACKUP_DIR="/tmp/unipay-portal-backup-$(date +%Y%m%d_%H%M%S)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/redhat-release ]]; then
        OS="centos"
        log_info "检测到 CentOS/RHEL 系统"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
        log_info "检测到 Debian/Ubuntu 系统"
    else
        log_error "不支持的操作系统"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装必要的依赖包..."
    
    if [[ $OS == "centos" ]]; then
        yum update -y
        yum install -y nginx wget curl unzip
    else
        apt-get update
        apt-get install -y nginx wget curl unzip
    fi
    
    log_success "依赖包安装完成"
}

# 创建备份
create_backup() {
    log_info "创建备份..."
    mkdir -p "$BACKUP_DIR"
    
    # 备份现有的项目文件
    if [[ -d "$PROJECT_PATH" ]]; then
        cp -r "$PROJECT_PATH" "$BACKUP_DIR/project"
        log_info "项目文件已备份到: $BACKUP_DIR/project"
    fi
    
    # 备份现有的nginx配置
    if [[ -f "$NGINX_CONF_PATH" ]]; then
        cp "$NGINX_CONF_PATH" "$BACKUP_DIR/nginx.conf.bak"
        log_info "Nginx配置已备份到: $BACKUP_DIR/nginx.conf.bak"
    fi
    
    log_success "备份创建完成"
}

# 创建项目目录
create_project_directory() {
    log_info "创建项目目录..."
    
    # 创建项目根目录
    mkdir -p "$PROJECT_PATH"
    
    # 创建日志目录
    mkdir -p /var/log/nginx
    
    log_success "项目目录创建完成"
}

# 部署项目文件
deploy_files() {
    log_info "部署项目文件..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制所有文件到项目目录
    cp "$SCRIPT_DIR"/*.html "$PROJECT_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/*.css "$PROJECT_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/*.js "$PROJECT_PATH/" 2>/dev/null || true
    cp "$SCRIPT_DIR"/*.md "$PROJECT_PATH/" 2>/dev/null || true
    
    # 设置文件权限
    chown -R www-data:www-data "$PROJECT_PATH" 2>/dev/null || chown -R nginx:nginx "$PROJECT_PATH" 2>/dev/null || true
    chmod -R 755 "$PROJECT_PATH"
    
    log_success "项目文件部署完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 复制nginx配置文件
    cp nginx.conf "$NGINX_CONF_PATH"
    
    # 创建软链接启用站点
    if [[ -d "/etc/nginx/sites-enabled" ]]; then
        ln -sf "$NGINX_CONF_PATH" "$NGINX_ENABLED_PATH"
    fi
    
    # 测试nginx配置
    if nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
    
    log_success "Nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查防火墙类型并开放端口
    if command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
        log_info "已通过firewalld开放HTTP/HTTPS端口"
    elif command -v ufw &> /dev/null; then
        # Ubuntu ufw
        ufw allow 'Nginx Full'
        log_info "已通过ufw开放Nginx端口"
    elif command -v iptables &> /dev/null; then
        # 传统iptables
        iptables -I INPUT -p tcp --dport 80 -j ACCEPT
        iptables -I INPUT -p tcp --dport 443 -j ACCEPT
        # 保存iptables规则
        if [[ $OS == "centos" ]]; then
            service iptables save 2>/dev/null || true
        else
            iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
        fi
        log_info "已通过iptables开放HTTP/HTTPS端口"
    else
        log_warning "未检测到防火墙，请手动开放80和443端口"
    fi
    
    log_success "防火墙配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动并启用nginx
    systemctl enable nginx
    systemctl restart nginx
    
    # 检查nginx状态
    if systemctl is-active --quiet nginx; then
        log_success "Nginx服务启动成功"
    else
        log_error "Nginx服务启动失败"
        systemctl status nginx
        exit 1
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":80 "; then
        log_success "HTTP端口(80)监听正常"
    else
        log_warning "HTTP端口(80)未监听"
    fi
    
    # 检查网站访问
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost" | grep -q "200"; then
        log_success "网站访问正常"
    else
        log_warning "网站访问可能存在问题"
    fi
    
    # 显示访问信息
    echo ""
    log_success "部署完成！"
    echo -e "${GREEN}网站访问地址:${NC}"
    echo -e "  HTTP:  http://$DOMAIN"
    echo -e "  HTTPS: https://$DOMAIN (需要配置SSL证书)"
    echo ""
    echo -e "${BLUE}平台入口:${NC}"
    echo -e "  运营平台: http://$DOMAIN/manager"
    echo -e "  代理商平台: http://$DOMAIN/agent"
    echo -e "  商户平台: http://$DOMAIN/merchant"
    echo ""
    echo -e "${YELLOW}备份位置:${NC} $BACKUP_DIR"
}

# SSL证书配置提示
ssl_setup_info() {
    echo ""
    log_info "SSL证书配置说明:"
    echo -e "${YELLOW}如需启用HTTPS，请执行以下步骤:${NC}"
    echo "1. 获取SSL证书 (推荐使用Let's Encrypt免费证书)"
    echo "2. 编辑nginx配置文件: $NGINX_CONF_PATH"
    echo "3. 取消注释SSL相关配置行"
    echo "4. 修改证书路径"
    echo "5. 重启nginx: systemctl restart nginx"
    echo ""
    echo -e "${BLUE}Let's Encrypt证书获取命令:${NC}"
    echo "certbot --nginx -d $DOMAIN -d www.$DOMAIN"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 错误处理
error_handler() {
    log_error "部署过程中发生错误，正在回滚..."
    
    # 恢复备份
    if [[ -d "$BACKUP_DIR" ]]; then
        if [[ -f "$BACKUP_DIR/nginx.conf.bak" ]]; then
            cp "$BACKUP_DIR/nginx.conf.bak" "$NGINX_CONF_PATH"
            log_info "已恢复nginx配置"
        fi
        
        if [[ -d "$BACKUP_DIR/project" ]]; then
            rm -rf "$PROJECT_PATH"
            cp -r "$BACKUP_DIR/project" "$PROJECT_PATH"
            log_info "已恢复项目文件"
        fi
        
        systemctl restart nginx 2>/dev/null || true
    fi
    
    log_error "部署失败，已回滚到之前状态"
    exit 1
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "         UniPay Portal 自动部署脚本 v2.0"
    echo "=================================================="
    echo -e "${NC}"
    
    # 设置错误处理
    trap error_handler ERR
    trap cleanup EXIT
    
    # 执行部署步骤
    check_root
    detect_os
    create_backup
    install_dependencies
    create_project_directory
    deploy_files
    configure_nginx
    configure_firewall
    start_services
    verify_deployment
    ssl_setup_info
    
    log_success "UniPay Portal 部署完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi