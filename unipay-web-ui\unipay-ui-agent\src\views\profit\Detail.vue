<template>
  <a-drawer
    title="分润记录详情"
    :width="600"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    @close="open = false"
  >
    <a-descriptions :column="1" bordered>
      <a-descriptions-item label="记录ID">
        {{ detailData.id }}
      </a-descriptions-item>
      <a-descriptions-item label="代理商号">
        {{ detailData.agentNo }}
      </a-descriptions-item>
      <a-descriptions-item label="商户号">
        {{ detailData.mchNo }}
      </a-descriptions-item>
      <a-descriptions-item label="支付订单号">
        <span style="color: #1890ff; cursor: pointer;" @click="copyText(detailData.payOrderId)">
          {{ detailData.payOrderId }}
          <copy-outlined style="margin-left: 4px;" />
        </span>
      </a-descriptions-item>
      <a-descriptions-item label="订单金额">
        <b style="color: #1890ff;">￥{{ (detailData.orderAmount / 100).toFixed(2) }}</b>
      </a-descriptions-item>
      <a-descriptions-item label="商户手续费">
        ￥{{ (detailData.mchFeeAmount / 100).toFixed(2) }}
      </a-descriptions-item>
      <a-descriptions-item label="分润比例">
        {{ (detailData.profitRate * 100).toFixed(2) }}%
      </a-descriptions-item>
      <a-descriptions-item label="分润金额">
        <b style="color: #3f8600;">￥{{ (detailData.profitAmount / 100).toFixed(2) }}</b>
      </a-descriptions-item>
      <a-descriptions-item label="分润状态">
        <a-tag v-if="detailData.state === 0" color="orange">待结算</a-tag>
        <a-tag v-if="detailData.state === 1" color="green">已结算</a-tag>
        <a-tag v-if="detailData.state === 2" color="red">已取消</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="分润日期">
        {{ detailData.profitDate }}
      </a-descriptions-item>
      <a-descriptions-item label="结算日期" v-if="detailData.settleDate">
        {{ detailData.settleDate }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ detailData.createdAt }}
      </a-descriptions-item>
      <a-descriptions-item label="更新时间" v-if="detailData.updatedAt">
        {{ detailData.updatedAt }}
      </a-descriptions-item>
      <a-descriptions-item label="备注" v-if="detailData.remark">
        {{ detailData.remark }}
      </a-descriptions-item>
    </a-descriptions>

    <!-- 分润计算说明 -->
    <a-card title="分润计算说明" style="margin-top: 16px;" size="small">
      <p><strong>计算公式：</strong></p>
      <p>分润金额 = 商户手续费 × 分润比例</p>
      <p><strong>本次计算：</strong></p>
      <p>
        ￥{{ (detailData.profitAmount / 100).toFixed(2) }} = 
        ￥{{ (detailData.mchFeeAmount / 100).toFixed(2) }} × 
        {{ (detailData.profitRate * 100).toFixed(2) }}%
      </p>
    </a-card>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_PROFIT_RECORD, req } from '@/api/manage'
import { reactive, getCurrentInstance } from 'vue'
import { CopyOutlined } from '@ant-design/icons-vue'

const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const vdata = reactive({
  open: false,
  detailData: {} as any, // 详情数据
})

// 解构响应式数据
const { open, detailData } = vdata

function show(recordId: number) {
  // 弹层打开事件
  vdata.detailData = {}
  
  req.getById(API_URL_PROFIT_RECORD, recordId).then((res: any) => {
    vdata.detailData = res
    vdata.open = true
  }).catch(() => {
    $infoBox.message.error('获取分润记录详情失败')
  })
}

function copyText(text: string) {
  if (!text) return
  
  navigator.clipboard.writeText(text).then(() => {
    $infoBox.message.success('复制成功')
  }).catch(() => {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      $infoBox.message.success('复制成功')
    } catch (err) {
      $infoBox.message.error('复制失败')
    }
    document.body.removeChild(textArea)
  })
}

// 暴露方法给父组件
defineExpose({
  show
})
</script>

<style scoped>
.ant-descriptions-item-label {
  width: 120px;
  font-weight: bold;
}
</style>
