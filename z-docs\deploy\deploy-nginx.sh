#!/bin/bash

# UniPay Nginx 部署脚本
# 使用方法: ./deploy-nginx.sh [single|multi]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查nginx是否安装
check_nginx() {
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装，请先安装nginx"
        log_info "Ubuntu/Debian: sudo apt-get install nginx"
        log_info "CentOS/RHEL: sudo yum install nginx"
        exit 1
    fi
    log_info "Nginx已安装: $(nginx -v 2>&1)"
}

# 备份现有配置
backup_config() {
    local backup_dir="/etc/nginx/backup/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    if [ -f "/etc/nginx/sites-available/unipay" ]; then
        cp "/etc/nginx/sites-available/unipay" "$backup_dir/"
        log_info "已备份现有配置到: $backup_dir"
    fi
}

# 部署多域名配置
deploy_multi() {
    log_info "部署多域名配置..."
    
    # 复制配置文件
    cp nginx-unipay.conf /etc/nginx/sites-available/unipay
    
    # 创建软链接
    if [ ! -L "/etc/nginx/sites-enabled/unipay" ]; then
        ln -s /etc/nginx/sites-available/unipay /etc/nginx/sites-enabled/
        log_info "已创建配置软链接"
    fi
    
    # 创建日志目录
    mkdir -p /var/log/nginx
    
    log_info "多域名配置部署完成"
    log_info "请确保DNS解析已配置:"
    log_info "  manager.unipay.com -> 服务器IP"
    log_info "  merchant.unipay.com -> 服务器IP"
    log_info "  agent.unipay.com -> 服务器IP"
    log_info "  pay.unipay.com -> 服务器IP"
    log_info "  game.unipay.com -> 服务器IP"
}

# 部署单域名配置
deploy_single() {
    log_info "部署单域名配置..."
    
    # 复制配置文件
    cp nginx-unipay-single.conf /etc/nginx/sites-available/unipay
    
    # 创建软链接
    if [ ! -L "/etc/nginx/sites-enabled/unipay" ]; then
        ln -s /etc/nginx/sites-available/unipay /etc/nginx/sites-enabled/
        log_info "已创建配置软链接"
    fi
    
    # 创建日志目录
    mkdir -p /var/log/nginx
    
    log_info "单域名配置部署完成"
    log_info "访问地址:"
    log_info "  管理后台: http://your-domain/manager/"
    log_info "  商户平台: http://your-domain/merchant/"
    log_info "  代理商平台: http://your-domain/agent/"
    log_info "  支付网关: http://your-domain/pay/"
    log_info "  游戏服务: http://your-domain/game/"
}

# 测试nginx配置
test_config() {
    log_info "测试Nginx配置..."
    if nginx -t; then
        log_info "Nginx配置测试通过"
        return 0
    else
        log_error "Nginx配置测试失败"
        return 1
    fi
}

# 重载nginx
reload_nginx() {
    log_info "重载Nginx配置..."
    if systemctl reload nginx; then
        log_info "Nginx重载成功"
    else
        log_error "Nginx重载失败"
        exit 1
    fi
}

# 显示状态
show_status() {
    log_info "检查服务状态..."
    echo "=== Nginx状态 ==="
    systemctl status nginx --no-pager -l
    
    echo -e "\n=== 端口监听状态 ==="
    netstat -tlnp | grep -E ':(80|443|8080|8088|9216|9218|9219)\s'
    
    echo -e "\n=== UniPay服务检查 ==="
    for port in 8080 8088 9216 9218 9219; do
        if netstat -tln | grep -q ":$port "; then
            echo -e "${GREEN}✓${NC} 端口 $port 正在监听"
        else
            echo -e "${RED}✗${NC} 端口 $port 未监听"
        fi
    done
}

# 主函数
main() {
    local deploy_type=${1:-"single"}
    
    log_info "开始部署UniPay Nginx配置..."
    
    check_root
    check_nginx
    backup_config
    
    case $deploy_type in
        "multi")
            deploy_multi
            ;;
        "single")
            deploy_single
            ;;
        *)
            log_error "无效的部署类型: $deploy_type"
            log_info "使用方法: $0 [single|multi]"
            exit 1
            ;;
    esac
    
    if test_config; then
        reload_nginx
        show_status
        log_info "部署完成！"
    else
        log_error "配置测试失败，请检查配置文件"
        exit 1
    fi
}

# 执行主函数
main "$@"