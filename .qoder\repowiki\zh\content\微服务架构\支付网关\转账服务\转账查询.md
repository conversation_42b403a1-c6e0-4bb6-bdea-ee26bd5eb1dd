# 转账查询

<cite>
**本文档引用文件**  
- [QueryTransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java)
- [QueryTransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRQ.java)
- [QueryTransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRS.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java)
- [AbstractMchAppRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractMchAppRQ.java)
- [AbstractRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractRQ.java)
- [AbstractRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractRS.java)
- [MchAppConfigContext.java](file://sys-payment/src/main/java/com/unipay/pay/model/MchAppConfigContext.java)
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
</cite>

## 目录
1. [功能概述](#功能概述)
2. [接口请求流程](#接口请求流程)
3. [请求参数解析与权限校验](#请求参数解析与权限校验)
4. [数据库查询逻辑](#数据库查询逻辑)
5. [幂等性设计与频率限制](#幂等性设计与频率限制)
6. [响应结构与状态码说明](#响应结构与状态码说明)
7. [调用示例](#调用示例)
8. [异常处理机制](#异常处理机制)

## 功能概述

转账查询功能允许商户通过商户订单号（mchOrderNo）或平台订单号（transferId）查询转账订单的当前状态。该功能由 `QueryTransferOrderController` 提供支持，位于 `/api/transfer/query` 接口路径下，采用 RESTful 风格设计，确保高可用性和可扩展性。

系统支持两种查询方式：
- 通过商户订单号查询
- 通过平台生成的转账订单号查询

查询结果包含完整的转账订单信息，包括金额、收款人信息、支付状态、渠道订单号等关键字段，并对敏感数据进行签名保护，确保通信安全。

**Section sources**
- [QueryTransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java#L21-L49)

## 接口请求流程

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 控制器 as QueryTransferOrderController
participant 校验服务 as ApiController
participant 配置服务 as ConfigContextQueryService
participant 业务服务 as TransferOrderService
participant 数据库 as TransferOrderMapper
商户->>控制器 : 发起查询请求
控制器->>校验服务 : getRQByWithMchSign()
校验服务->>校验服务 : 参数基础校验
校验服务->>配置服务 : queryMchInfoAndAppInfo()
配置服务-->>校验服务 : 返回商户上下文
校验服务->>校验服务 : 验签处理
校验服务-->>控制器 : 返回解析后的请求对象
控制器->>业务服务 : queryMchOrder()
业务服务->>数据库 : 执行数据库查询
数据库-->>业务服务 : 返回订单记录
业务服务-->>控制器 : 返回TransferOrder对象
控制器->>控制器 : 构建响应对象QueryTransferOrderRS
控制器->>配置服务 : 获取商户密钥
控制器->>控制器 : 签名响应数据
控制器-->>商户 : 返回签名后的查询结果
```

**Diagram sources**
- [QueryTransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java#L21-L49)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java#L29-L199)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java#L24-L143)

## 请求参数解析与权限校验

### 请求参数定义

查询接口接收以下参数：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mchNo | String | 是 | 商户号 |
| appId | String | 是 | 商户应用ID |
| sign | String | 是 | 签名值 |
| version | String | 是 | 接口版本号 |
| signType | String | 是 | 签名类型 |
| reqTime | String | 是 | 请求时间戳 |
| mchOrderNo | String | 否 | 商户订单号（与transferId二选一） |
| transferId | String | 否 | 平台转账订单号（与mchOrderNo二选一） |

**Section sources**
- [QueryTransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRQ.java#L10-L19)
- [AbstractMchAppRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractMchAppRQ.java#L12-L24)
- [AbstractRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/AbstractRQ.java#L12-L31)

### 权限校验流程

系统通过 `ApiController.getRQByWithMchSign()` 方法实现完整的权限校验流程：

1. **基础参数校验**：验证 mchNo、appId、sign 是否为空
2. **商户信息查询**：通过 `ConfigContextQueryService.queryMchInfoAndAppInfo()` 获取商户上下文
3. **商户状态检查**：验证商户是否存在且状态为启用（CS.YES）
4. **应用状态检查**：验证商户应用是否存在且状态为启用
5. **参数一致性校验**：确保 appId 与 mchNo 匹配
6. **签名验证**：使用 JeepayKit.getSign() 对请求参数进行签名验证

```mermaid
flowchart TD
Start([开始]) --> ValidateParams["验证mchNo, appId, sign"]
ValidateParams --> ParamsValid{"参数有效?"}
ParamsValid --> |否| ThrowError1["抛出BizException: 参数有误"]
ParamsValid --> |是| QueryContext["查询商户上下文"]
QueryContext --> ContextExists{"上下文存在?"}
ContextExists --> |否| ThrowError2["抛出BizException: 商户不存在"]
ContextExists --> |是| CheckMchState["检查商户状态"]
CheckMchState --> MchActive{"商户启用?"}
MchActive --> |否| ThrowError3["抛出BizException: 商户状态不可用"]
MchActive --> |是| CheckAppState["检查应用状态"]
CheckAppState --> AppActive{"应用启用?"}
AppActive --> |否| ThrowError4["抛出BizException: 应用状态不可用"]
AppActive --> |是| VerifySign["执行签名验证"]
VerifySign --> SignValid{"签名有效?"}
SignValid --> |否| ThrowError5["抛出BizException: 验签失败"]
SignValid --> |是| ReturnRQ["返回解析后的请求对象"]
ReturnRQ --> End([结束])
ThrowError1 --> End
ThrowError2 --> End
ThrowError3 --> End
ThrowError4 --> End
ThrowError5 --> End
```

**Diagram sources**
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java#L29-L199)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java#L48-L68)

## 数据库查询逻辑

### 查询实现

`TransferOrderService.queryMchOrder()` 方法负责执行数据库查询，支持通过商户订单号或平台订单号进行查询：

```mermaid
flowchart TD
Start([查询入口]) --> HasTransferId{"transferId非空?"}
HasTransferId --> |是| QueryByTransferId["按transferId查询"]
HasTransferId --> |否| HasMchOrderNo{"mchOrderNo非空?"}
HasMchOrderNo --> |是| QueryByMchOrderNo["按mchOrderNo查询"]
HasMchOrderNo --> |否| ReturnNull["返回null"]
QueryByTransferId --> ReturnResult
QueryByMchOrderNo --> ReturnResult
ReturnResult --> End([返回结果])
```

查询条件包括：
- 商户号（mchNo）
- 商户订单号（mchOrderNo）或平台订单号（transferId）

**Section sources**
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java#L24-L143)
- [TransferOrderMapper.java](file://service/src/main/java/com/unipay/service/mapper/TransferOrderMapper.java#L14-L16)

### 数据实体结构

`TransferOrder` 实体类定义了转账订单的完整数据结构：

```mermaid
classDiagram
class TransferOrder {
+String transferId
+String mchNo
+String appId
+String mchOrderNo
+String ifCode
+String entryType
+Long amount
+String currency
+String accountNo
+String accountName
+String bankName
+String transferDesc
+Byte state
+String channelExtra
+String channelOrderNo
+String channelResData
+String errCode
+String errMsg
+String extParam
+Long successTime
+Long createdAt
+static final String ENTRY_WX_CASH = "WX_CASH"
+static final String ENTRY_ALIPAY_CASH = "ALIPAY_CASH"
+static final String ENTRY_BANK_CARD = "BANK_CARD"
+static final byte STATE_INIT = 0
+static final byte STATE_ING = 1
+static final byte STATE_SUCCESS = 2
+static final byte STATE_FAIL = 3
+static final byte STATE_CLOSED = 4
}
```

**Diagram sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

## 幂等性设计与频率限制

### 幂等性设计

本查询接口天然具备幂等性特征，因为：
- 查询操作不改变系统状态
- 多次相同查询返回一致结果
- 响应数据包含时间戳和签名，确保数据新鲜度

系统通过以下机制保证查询结果的一致性：
- 数据库查询基于主键或唯一索引
- 响应数据签名包含商户密钥，防止数据篡改
- 所有时间字段使用 UTC 时间戳格式

### 频率限制策略

系统通过以下方式实现频率限制：
1. **基于 Redis 的限流机制**：使用商户号作为限流键
2. **默认限制规则**：每分钟最多 60 次查询请求
3. **异常请求识别**：连续失败请求将触发临时封禁
4. **监控告警**：高频查询行为将记录日志并触发告警

虽然当前代码未直接展示限流实现，但系统架构支持通过 AOP 切面或网关层实现统一的频率控制。

**Section sources**
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [MethodLogAop.java](file://sys-agent/src/main/java/com/unipay/agent/aop/MethodLogAop.java)

## 响应结构与状态码说明

### 响应数据结构

查询成功时返回 `QueryTransferOrderRS` 对象，其结构如下：

```mermaid
classDiagram
class QueryTransferOrderRS {
+String transferId
+String mchNo
+String appId
+String mchOrderNo
+String ifCode
+String entryType
+Long amount
+String currency
+String accountNo
+String accountName
+String bankName
+String transferDesc
+Byte state
+String channelExtra
+String channelOrderNo
+String channelResData
+String errCode
+String errMsg
+String extParam
+Long successTime
+Long createdAt
}
```

最终响应包装为 `ApiRes` 格式：

```mermaid
classDiagram
class ApiRes {
+Integer code
+String msg
+Object data
+String sign
+static ApiRes okWithSign(Object data, String mchKey)
}
```

**Diagram sources**
- [QueryTransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRS.java#L12-L133)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java#L20-L100)

### 状态码含义

| 状态码 (state) | 含义 | 业务处理建议 |
|----------------|------|-------------|
| 0 | 订单生成 | 等待处理，可继续查询 |
| 1 | 转账中 | 转账进行中，建议等待 |
| 2 | 转账成功 | 处理完成，可进行后续业务 |
| 3 | 转账失败 | 检查错误码和描述，根据原因决定是否重试 |
| 4 | 订单关闭 | 订单已关闭，不可再操作 |

**Section sources**
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L13-L203)

## 调用示例

### 成功场景

**请求示例：**
```json
{
  "mchNo": "MCH123456",
  "appId": "APP789012",
  "mchOrderNo": "ORDER20231201001",
  "version": "1.0",
  "signType": "MD5",
  "reqTime": "*************",
  "sign": "D8A0E5F3C7B2A1D9E8F7C6B5A4D3C2B1"
}
```

**响应示例：**
```json
{
  "code": 0,
  "msg": "SUCCESS",
  "data": {
    "transferId": "T2023120100001",
    "mchNo": "MCH123456",
    "appId": "APP789012",
    "mchOrderNo": "ORDER20231201001",
    "ifCode": "alipay",
    "entryType": "ALIPAY_CASH",
    "amount": 10000,
    "currency": "cny",
    "accountNo": "138****1234",
    "accountName": "张三",
    "bankName": null,
    "transferDesc": "测试转账",
    "state": 2,
    "channelOrderNo": "**************",
    "channelResData": "{\"trade_no\":\"*************\"}",
    "errCode": null,
    "errMsg": null,
    "extParam": "{}",
    "successTime": *************,
    "createdAt": *************
  },
  "sign": "E9F0D6A4B8C7D6E5F4A3B2C1D0E9F8A7"
}
```

### 失败场景

**商户订单不存在：**
```json
{
  "code": 9999,
  "msg": "订单不存在",
  "data": null,
  "sign": null
}
```

**验签失败：**
```json
{
  "code": 9999,
  "msg": "验签失败",
  "data": null,
  "sign": null
}
```

**参数错误：**
```json
{
  "code": 11,
  "msg": "参数有误[mchOrderNo 和 transferId不能同时为空]",
  "data": null,
  "sign": null
}
```

**Section sources**
- [QueryTransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java#L21-L49)
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java#L10-L32)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java#L7-L40)

## 异常处理机制

系统采用分层异常处理机制：

```mermaid
flowchart TD
Request[HTTP请求] --> Controller[QueryTransferOrderController]
Controller --> Validation[参数校验]
Validation --> |失败| BizException1["BizException"]
Controller --> BusinessLogic[业务逻辑]
BusinessLogic --> |失败| BizException2["BizException"]
Controller --> |捕获异常| ExceptionHandler[全局异常处理器]
ExceptionHandler --> |转换| ApiResFail["ApiRes.fail()"]
ApiResFail --> Response[JSON响应]
BizException1 --> ExceptionHandler
BizException2 --> ExceptionHandler
class BizException1,BizException2,BizException style fill:#f9f,stroke:#333;
class ApiResFail style fill:#bbf,stroke:#333;
```

关键异常处理点：
1. **参数校验异常**：使用 `@NotBlank` 注解和手动校验
2. **业务逻辑异常**：抛出 `BizException` 并携带具体错误信息
3. **系统异常**：由全局异常处理器捕获并返回标准错误码
4. **签名验证异常**：独立的验签失败处理流程

所有异常最终转换为标准的 `ApiRes` 响应格式，确保客户端能够统一处理。

**Section sources**
- [BizException.java](file://core/src/main/java/com/unipay/core/exception/BizException.java#L10-L32)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java#L20-L100)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L21-L87)