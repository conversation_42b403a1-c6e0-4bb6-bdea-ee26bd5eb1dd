<template>
  <page-header-wrapper>
    <a-card>
      <div v-if="$access('ENT_PROFIT_RECORD_LIST')" class="table-page-search-wrapper">
        <a-form layout="inline" class="table-head-ground">
          <div class="table-layer">
            <jeepay-text-up v-model:value="searchData.mchNo" :placeholder="'商户号'" />
            <a-select
              v-model:value="searchData.state"
              placeholder="分润状态"
              class="table-head-layout"
            >
              <a-select-option key="">全部状态</a-select-option>
              <a-select-option :value="0">待结算</a-select-option>
              <a-select-option :value="1">已结算</a-select-option>
              <a-select-option :value="2">已取消</a-select-option>
            </a-select>
            <a-range-picker
              v-model:value="dateRange"
              :placeholder="['开始日期', '结束日期']"
              class="table-head-layout"
              @change="onDateChange"
            />
            <span class="table-head-layout">
              <a-button type="primary" :loading="btnLoading" @click="queryFunc">
                查询
              </a-button>
              <a-button style="margin-left: 8px" @click="resetSearch">重置</a-button>
            </span>
          </div>
        </a-form>
      </div>

      <!-- 统计信息卡片 -->
      <div v-if="$access('ENT_PROFIT_RECORD_STATISTICS')" class="statistics-cards" style="margin-bottom: 16px;">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="总分润金额"
                :value="statistics.totalAmount || 0"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#3f8600' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="已结算金额"
                :value="statistics.settledAmount || 0"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="待结算金额"
                :value="statistics.waitSettleAmount || 0"
                :precision="2"
                suffix="元"
                :value-style="{ color: '#faad14' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic
                title="分润记录数"
                :value="statistics.totalCount || 0"
                suffix="笔"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 列表渲染 -->
      <JeepayTable
        @btnLoadClose="btnLoading = false"
        ref="infoTable"
        :initData="true"
        :reqTableDataFunc="reqTableDataFunc"
        :tableColumns="tableColumns"
        :searchData="searchData"
        rowKey="id"
        :tableRowCrossColor="true"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'orderAmount'">
            <b>￥{{ (record.orderAmount / 100).toFixed(2) }}</b>
          </template>
          <template v-if="column.key === 'mchFeeAmount'">
            ￥{{ (record.mchFeeAmount / 100).toFixed(2) }}
          </template>
          <template v-if="column.key === 'profitAmount'">
            <b style="color: #3f8600;">￥{{ (record.profitAmount / 100).toFixed(2) }}</b>
          </template>
          <template v-if="column.key === 'profitRate'">
            {{ (record.profitRate * 100).toFixed(2) }}%
          </template>
          <template v-if="column.key === 'state'">
            <a-tag v-if="record.state === 0" color="orange">待结算</a-tag>
            <a-tag v-if="record.state === 1" color="green">已结算</a-tag>
            <a-tag v-if="record.state === 2" color="red">已取消</a-tag>
          </template>
          <template v-if="column.key === 'op'">
            <!-- 操作列插槽 -->
            <JeepayTableColumns>
              <a-button
                type="link"
                v-if="$access('ENT_PROFIT_RECORD_VIEW')"
                @click="viewFunc(record.id)"
              >
                详情
              </a-button>
            </JeepayTableColumns>
          </template>
        </template>
      </JeepayTable>

      <!-- 详情页面组件 -->
      <Detail ref="profitDetail" />
    </a-card>
  </page-header-wrapper>
</template>

<script setup lang="ts">
import { API_URL_PROFIT_RECORD, req } from '@/api/manage'
import request from '@/http/request'
import Detail from './Detail.vue'
import { reactive, ref, onMounted, getCurrentInstance } from 'vue'
import dayjs from 'dayjs'

const { $infoBox, $access } = getCurrentInstance()!.appContext.config.globalProperties

// 表格列定义
const tableColumns = [
  { key: 'agentNo', title: '代理商号', dataIndex: 'agentNo', width: '120px' },
  { key: 'mchNo', title: '商户号', dataIndex: 'mchNo', width: '120px' },
  { key: 'payOrderId', title: '支付订单号', dataIndex: 'payOrderId', width: '180px' },
  { key: 'orderAmount', title: '订单金额', width: '100px' },
  { key: 'mchFeeAmount', title: '商户手续费', width: '100px' },
  { key: 'profitRate', title: '分润比例', width: '80px' },
  { key: 'profitAmount', title: '分润金额', width: '100px' },
  { key: 'state', title: '分润状态', width: '80px' },
  { key: 'profitDate', title: '分润日期', dataIndex: 'profitDate', width: '120px' },
  { key: 'settleDate', title: '结算日期', dataIndex: 'settleDate', width: '120px' },
  {
    key: 'op',
    title: '操作',
    width: '80px',
    fixed: 'right',
    align: 'center',
  },
]

const searchData = reactive({
  mchNo: '',
  state: '',
  startDate: '',
  endDate: ''
})

const statistics = reactive({
  totalAmount: 0,
  settledAmount: 0,
  waitSettleAmount: 0,
  totalCount: 0
})

const btnLoading = ref(false)
const dateRange = ref([])
const infoTable = ref()
const profitDetail = ref()

onMounted(() => {
  loadStatistics()
})

function queryFunc() {
  btnLoading.value = true
  infoTable.value.refTable(true)
  loadStatistics()
}

function resetSearch() {
  searchData.mchNo = ''
  searchData.state = ''
  searchData.startDate = ''
  searchData.endDate = ''
  dateRange.value = []
}

function onDateChange(dates: any) {
  if (dates && dates.length === 2) {
    searchData.startDate = dayjs(dates[0]).format('YYYY-MM-DD')
    searchData.endDate = dayjs(dates[1]).format('YYYY-MM-DD')
  } else {
    searchData.startDate = ''
    searchData.endDate = ''
  }
}

// 请求table接口数据
function reqTableDataFunc(params: any) {
  return req.list(API_URL_PROFIT_RECORD, params)
}

function viewFunc(recordId: number) {
  profitDetail.value.show(recordId)
}

// 加载统计数据
function loadStatistics() {
  if (!$access('ENT_PROFIT_RECORD_STATISTICS')) {
    return
  }
  
  const params: any = {}
  if (searchData.state !== '') {
    params.state = searchData.state
  }
  if (searchData.startDate) {
    params.startDate = searchData.startDate
  }
  if (searchData.endDate) {
    params.endDate = searchData.endDate
  }

  request.request({
    url: API_URL_PROFIT_RECORD + '/statistics',
    method: 'GET',
    params: params
  }).then((res: any) => {
    statistics.totalAmount = (res.totalAmount || 0) / 100
    statistics.settledAmount = (res.settledAmount || 0) / 100
    statistics.waitSettleAmount = (res.waitSettleAmount || 0) / 100
    statistics.totalCount = res.totalCount || 0
  }).catch(() => {
    // 统计数据加载失败时不影响主要功能
  })
}
</script>

<style scoped>
.statistics-cards {
  margin-bottom: 16px;
}
</style>
