# UniPay 门户网站

这是 UniPay 统一支付平台的门户首页，提供美观的界面来访问各个子系统。

## 功能特性

- 🎨 现代化响应式设计
- 🚀 流畅的动画效果
- 📱 移动端适配
- 🔗 一键跳转到各个平台
- ⌨️ 键盘快捷键支持
- 🔒 安全的反向代理配置

## 平台入口

- **运营平台** (端口 9217): 系统管理、用户管理、数据统计
- **代理商平台** (端口 9219): 代理商管理、商户发展、分润结算
- **商户平台** (端口 9218): 支付配置、订单管理、财务对账

## 文件结构

```
unipay-portal/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript 功能
├── nginx.conf          # Nginx 配置文件
└── README.md           # 说明文档
```

## 部署步骤

### 1. 准备文件

将所有文件上传到服务器的 web 目录，例如：
```bash
/var/www/unipay-portal/
```

### 2. 配置 Nginx

1. 复制 `nginx.conf` 到 Nginx 配置目录：
```bash
sudo cp nginx.conf /etc/nginx/sites-available/unipay-portal
```

2. 创建软链接启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/unipay-portal /etc/nginx/sites-enabled/
```

3. 修改配置文件中的域名和路径：
```bash
sudo nano /etc/nginx/sites-available/unipay-portal
```

需要修改的地方：
- `server_name`: 替换为您的域名
- `root`: 替换为实际的文件路径
- SSL 证书路径（如果使用 HTTPS）

4. 测试配置并重启 Nginx：
```bash
sudo nginx -t
sudo systemctl reload nginx
```

### 3. 设置文件权限

```bash
sudo chown -R www-data:www-data /var/www/unipay-portal
sudo chmod -R 755 /var/www/unipay-portal
```

### 4. 配置防火墙

确保以下端口开放：
- 80 (HTTP)
- 443 (HTTPS，如果使用)
- 9216 (支付网关)
- 9217 (运营平台)
- 9218 (商户平台)
- 9219 (代理商平台)

```bash
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 9216
sudo ufw allow 9217
sudo ufw allow 9218
sudo ufw allow 9219
```

## 自定义配置

### 修改平台端口

如果您的平台使用不同的端口，请修改 `script.js` 中的配置：

```javascript
const platformConfig = {
    manager: {
        name: '运营平台',
        port: '9217',    // 修改为实际端口
        path: '/'
    },
    agent: {
        name: '代理商平台', 
        port: '9219',    // 修改为实际端口
        path: '/'
    },
    merchant: {
        name: '商户平台',
        port: '9218',    // 修改为实际端口
        path: '/'
    }
};
```

### 修改样式

您可以通过修改 `styles.css` 来自定义：
- 颜色主题
- 字体样式
- 动画效果
- 布局结构

### 添加新平台

1. 在 `script.js` 的 `platformConfig` 中添加新平台配置
2. 在 `index.html` 中添加对应的平台卡片
3. 在 `nginx.conf` 中添加相应的代理配置

## 键盘快捷键

- `Alt + 1`: 跳转到运营平台
- `Alt + 2`: 跳转到代理商平台
- `Alt + 3`: 跳转到商户平台

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## HTTPS 配置

如果需要启用 HTTPS，请：

1. 获取 SSL 证书（Let's Encrypt 推荐）
2. 取消注释 `nginx.conf` 中的 SSL 配置
3. 修改证书路径
4. 启用 HTTP 到 HTTPS 的重定向

### 使用 Let's Encrypt

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 性能优化

1. **启用 Gzip 压缩**（已在配置中包含）
2. **设置静态资源缓存**（已在配置中包含）
3. **使用 CDN** 加速静态资源加载
4. **优化图片** 使用 WebP 格式

## 监控和日志

日志文件位置：
- 访问日志: `/var/log/nginx/unipay-portal.access.log`
- 错误日志: `/var/log/nginx/unipay-portal.error.log`

监控命令：
```bash
# 实时查看访问日志
sudo tail -f /var/log/nginx/unipay-portal.access.log

# 实时查看错误日志
sudo tail -f /var/log/nginx/unipay-portal.error.log
```

## 故障排除

### 常见问题

1. **页面无法访问**
   - 检查 Nginx 配置是否正确
   - 确认端口是否开放
   - 查看错误日志

2. **平台跳转失败**
   - 确认后端服务是否运行
   - 检查端口配置是否正确
   - 查看浏览器控制台错误

3. **样式显示异常**
   - 检查文件权限
   - 确认静态资源路径正确
   - 清除浏览器缓存

### 调试模式

在浏览器控制台中输入以下命令启用调试：
```javascript
localStorage.setItem('debug', 'true');
```

## 更新日志

- v1.0.0: 初始版本，包含基本功能和三个平台入口
- 支持响应式设计和动画效果
- 集成 Nginx 反向代理配置

## 技术支持

如有问题，请检查：
1. Nginx 错误日志
2. 浏览器开发者工具
3. 后端服务状态

## 许可证

本项目采用 MIT 许可证。
