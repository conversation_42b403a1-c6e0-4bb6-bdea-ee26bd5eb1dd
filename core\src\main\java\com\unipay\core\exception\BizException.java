
package com.unipay.core.exception;

import com.unipay.core.constants.ApiCodeEnum;
import com.unipay.core.model.ApiRes;
import lombok.Getter;
/*
* 自定义业务异常 
* @date 2021/6/8 16:33
*/
@Getter
public class BizException extends RuntimeException{

	private static final long serialVersionUID = 1L;

	private ApiRes apiRes;

	/** 业务自定义异常 **/
	public BizException(String msg) {
		super(msg);
		this.apiRes = ApiRes.customFail(msg);
	}

	public BizException(ApiCodeEnum apiCodeEnum, String... params) {
		super();
		apiRes = ApiRes.fail(apiCodeEnum, params);
	}

	public BizException(ApiRes apiRes) {
		super(apiRes.getMsg());
		this.apiRes = apiRes;
	}
}
