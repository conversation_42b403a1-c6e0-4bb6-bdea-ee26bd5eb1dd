
package com.unipay.core.cache;

import com.alibaba.fastjson.JSON;
import com.unipay.core.utils.SpringBeansUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/*
* Redis工具类 
* @date 2021/5/24 17:58
*/
public class RedisUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtil.class);
    private static StringRedisTemplate stringRedisTemplate = null;

    /** 获取RedisTemplate对象, 默认使用 StringRedisTemplate, 客户端可查询 **/
    private static final RedisTemplate getStringRedisTemplate(){

        if(stringRedisTemplate == null){

            if(SpringBeansUtil.getApplicationContext().containsBean("defaultStringRedisTemplate")){
                stringRedisTemplate = SpringBeansUtil.getBean("defaultStringRedisTemplate", StringRedisTemplate.class);
            }else{
                stringRedisTemplate = SpringBeansUtil.getBean(StringRedisTemplate.class);
            }
        }
        return stringRedisTemplate;
    }

    /** Redis操作重试机制 */
    private static <T> T executeWithRetry(Supplier<T> operation, int maxRetries) {
        Exception lastException = null;
        for (int i = 0; i < maxRetries; i++) {
            try {
                return operation.get();
            } catch (Exception e) {
                lastException = e;
                logger.warn("Redis操作失败，第{}次重试，错误信息: {}", i + 1, e.getMessage());
                if (i < maxRetries - 1) {
                    try {
                        // 重试前等待一段时间，避免频繁重试
                        Thread.sleep(100 * (i + 1)); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        logger.error("Redis操作失败，已重试{}次", maxRetries, lastException);
        throw new RuntimeException("Redis操作失败，已重试" + maxRetries + "次", lastException);
    }

    /** 获取缓存数据, String类型 - 带重试机制 */
    public static String getString(String key) {
        if(key == null) {
            return null;
        }
        return executeWithRetry(() -> (String)getStringRedisTemplate().opsForValue().get(key), 3);
    }

    /** 获取缓存数据对象 */
    public static <T> T getObject(String key, Class<T> cls) {

        String val = getString(key);
        return JSON.parseObject(val, cls);
    }

    /** 放置缓存对象 - 带重试机制 */
    public static void setString(String key, String value) {
        executeWithRetry(() -> {
            getStringRedisTemplate().opsForValue().set(key, value);
            return null;
        }, 3);
    }

    /** 普通缓存放入并设置时间, 默认单位：秒 - 带重试机制 */
    public static void setString(String key, String value, long time) {
        executeWithRetry(() -> {
            getStringRedisTemplate().opsForValue().set(key, value, time, TimeUnit.SECONDS);
            return null;
        }, 3);
    }

    /** 普通缓存放入并设置时间 - 带重试机制 */
    public static void setString(String key, String value, long time, TimeUnit timeUnit) {
        executeWithRetry(() -> {
            getStringRedisTemplate().opsForValue().set(key, value, time, timeUnit);
            return null;
        }, 3);
    }

    /** 放置缓存对象 */
    public static void set(String key, Object value) {
        setString(key, JSON.toJSONString(value));
    }

    /** 普通缓存放入并设置时间, 默认单位：秒 */
    public static void set(String key, Object value, long time) {
        setString(key, JSON.toJSONString(value), time);
    }

    /** 普通缓存放入并设置时间 */
    public static void set(String key, Object value, long time, TimeUnit timeUnit) {
        setString(key, JSON.toJSONString(value), time, timeUnit);
    }

    /** 指定缓存失效时间 */
    public static void expire(String key, long time) {
       getStringRedisTemplate().expire(key, time, TimeUnit.SECONDS);
    }

    /** 指定缓存失效时间 */
    public static void expire(String key, long time, TimeUnit timeUnit) {
        getStringRedisTemplate().expire(key, time, timeUnit);
    }

    /**
     * 根据key 获取过期时间
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public static long getExpire(String key) {
        return getStringRedisTemplate().getExpire(key, TimeUnit.SECONDS);
    }

    /** 判断key是否存在 */
    public static boolean hasKey(String key) {
        return getStringRedisTemplate().hasKey(key);
    }

    /** 删除缓存 **/
    public static void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                getStringRedisTemplate().delete(key[0]);
            } else {
                getStringRedisTemplate().delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /** 查询keys */
    public static Collection<String> keys(String pattern) {
        return getStringRedisTemplate().keys(pattern);
    }

}
