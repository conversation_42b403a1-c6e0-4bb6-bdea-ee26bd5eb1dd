
# 商户管理

<cite>
**本文档引用的文件**   
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java)
- [MchInfo.java](file://core\src\main\java\com\unipay\core\entity\MchInfo.java)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java)
- [MchInfoService.java](file://service\src\main\java\com\unipay\service\impl\MchInfoService.java)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java)
- [AgentMchRelationService.java](file://service\src\main\java\com\unipay\service\impl\AgentMchRelationService.java)
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java)
- [CleanMchLoginAuthCacheMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\CleanMchLoginAuthCacheMQ.java)
</cite>

## 目录
1. [代理商商户管理概述](#代理商商户管理概述)
2. [商户信息管理](#商户信息管理)
3. [商户应用管理](#商户应用管理)
4. [访问控制与权限验证](#访问控制与权限验证)
5. [数据同步与通知机制](#数据同步与通知机制)
6. [批量管理场景与实现](#批量管理场景与实现)

## 代理商商户管理概述

在统一支付系统中，代理商系统（sys-agent）为代理商提供了管理其下级商户的完整功能。代理商可以创建、配置、查看和删除其代理的商户，同时管理商户的应用、支付通道和接口参数。系统通过严格的权限控制确保代理商只能操作其代理范围内的商户，保障了数据的安全性和隔离性。

**本文档引用的文件**   
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java)

## 商户信息管理

代理商通过 `MchInfoController` 提供的API来管理商户的基本信息。核心操作包括商户的创建、查询、更新和删除。

### 商户创建

当代理商创建新商户时，系统会执行以下流程：
1.  **权限验证**：验证当前登录的代理商是否有权创建商户。
2.  **数据初始化**：自动生成商户号（`mchNo`），并设置当前代理商号（`agentNo`）作为商户的归属代理商。
3.  **事务性创建**：在一个数据库事务中，同时创建商户信息（`MchInfo`）和商户的初始管理员用户。
4.  **建立代理关系**：在 `t_agent_mch_relation` 表中创建一条记录，建立代理商与新商户的直接关系。
5.  **密码处理**：如果代理商指定了自定义密码，则在创建后立即更新商户管理员的登录密码。

```mermaid
sequenceDiagram
participant 代理商 as 代理商系统
participant MchInfoController as MchInfoController
participant MchInfoService as MchInfoService
participant AgentMchRelationService as AgentMchRelationService
participant SysUserAuthService as SysUserAuthService
代理商->>MchInfoController : POST /api/mchInfo (商户信息)
MchInfoController->>MchInfoController : 验证权限和参数
MchInfoController->>MchInfoService : 调用 addMch() 创建商户和用户
MchInfoService->>MchInfoService : 保存 MchInfo
MchInfoService->>MchInfoService : 保存 SysUser (管理员)
MchInfoService->>MchInfoService : 保存 MchApp (默认应用)
MchInfoService-->>MchInfoController : 成功
MchInfoController->>AgentMchRelationService : 创建代理商-商户关系
AgentMchRelationService->>AgentMchRelationService : 保存 AgentMchRelation
alt 提供了自定义密码
MchInfoController->>SysUserAuthService : 调用 resetAuthInfo() 更新密码
end
MchInfoController-->>代理商 : 返回成功响应
```

**Diagram sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L134-L203)
- [MchInfoService.java](file://service\src\main\java\com\unipay\service\impl\MchInfoService.java#L46-L99)
- [AgentMchRelationService.java](file://service\src\main\java\com\unipay\service\impl\AgentMchRelationService.java#L92-L114)

**Section sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L134-L203)
- [MchInfoService.java](file://service\src\main\java\com\unipay\service\impl\MchInfoService.java#L46-L99)

### 商户查询

代理商可以查询其代理的所有商户列表或单个商户的详细信息。查询时，系统会：
1.  **获取权限范围**：通过 `AgentMchRelationService.getMchNosByAgentNo()` 获取当前代理商有权管理的所有商户号列表。
2.  **数据过滤**：在查询 `t_mch_info` 表时，使用 `IN` 条件将查询范围限制在上述商户号列表内，确保数据隔离。
3.  **返回结果**：返回符合查询条件且在权限范围内的商户信息。

```mermaid
flowchart TD
A[代理商发起查询] --> B{获取代理商号}
B --> C[调用 getMchNosByAgentNo()]
C --> D[获取商户号列表]
D --> E{列表为空?}
E --> |是| F[返回空结果]
E --> |否| G[构建查询条件: IN (商户号列表)]
G --> H[查询 t_mch_info 表]
H --> I[返回商户列表]
```

**Diagram sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L53-L100)
- [AgentMchRelationService.java](file://service\src\main\java\com\unipay\service\impl\AgentMchRelationService.java#L36-L46)

**Section sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L53-L100)

### 商户更新与删除

-   **更新**：代理商可以更新商户的名称、联系人等基本信息。系统会验证该商户是否在当前代理商的管理范围内，然后更新 `t_mch_info` 表。
-   **删除**：删除操作是级联的。系统会：
    1.  验证权限。
    2.  调用 `MchInfoService.removeByMchNo()` 删除商户及其所有关联数据（如应用、用户、支付配置等）。
    3.  删除 `t_agent_mch_relation` 表中对应的代理关系记录。
    4.  发送消息清除相关用户的Redis缓存。

**Section sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L209-L279)

## 商户应用管理

每个商户可以拥有一个或多个应用（`MchApp`），用于隔离不同的业务场景或配置。代理商可以为其代理的商户管理这些应用。

### 应用的创建与配置

代理商可以为下属商户创建应用，并配置其支付通道和接口参数。

1.  **创建应用**：通过 `MchAppController.add()` 创建应用。系统会验证代理商对指定商户的管理权限，然后在 `t_mch_app` 表中插入记录。
2.  **配置支付通道**：应用需要配置支付通道（`MchPayPassage`），指定支持的支付方式（如微信支付、支付宝）和对应的费率。
3.  **配置接口参数**：应用需要配置具体的支付接口参数（`PayInterfaceConfig`），如商户ID、密钥等，这些参数是调用第三方支付API所必需的。

```mermaid
classDiagram
class MchApp {
+appId : String
+mchNo : String
+appName : String
+appSecret : String
+state : Byte
}
class MchPayPassage {
+id : Long
+mchNo : String
+appId : String
+ifCode : String
+wayCode : String
+rate : BigDecimal
+state : Byte
}
class PayInterfaceConfig {
+id : Long
+infoType : Byte
+infoId : String
+ifCode : String
+ifParams : String
+ifRate : BigDecimal
+state : Byte
}
MchApp "1" -- "0..*" MchPayPassage : 拥有
MchApp "1" -- "0..*" PayInterfaceConfig : 配置
```

**Diagram sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)
- [PayInterfaceConfig.java](file://core\src\main\java\com\unipay\core\entity\PayInterfaceConfig.java#L25-L124)

**Section sources**
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java#L115-L153)

## 访问控制与权限验证

系统的访问控制是基于角色和权限的。`MchInfoController` 和 `MchAppController` 中的方法使用 `@PreAuthorize` 注解来声明所需的权限。

-   **权限检查**：例如，`@PreAuthorize("hasAuthority('ENT_MCH_INFO_ADD')")` 确保只有拥有 `ENT_MCH_INFO_ADD` 权限的用户才能调用新增商户的接口。
-   **数据级权限**：除了功能权限，系统还实现了数据级权限控制。在 `list()`、`detail()`、`update()` 和 `delete()` 等方法中，代码会显式地调用 `agentMchRelationService.getMchNosByAgentNo()` 来获取当前代理商的商户范围，并将此范围作为查询条件，从根本上防止了越权访问。

**Section sources**
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L53-L279)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java#L49-L264)

## 数据同步与通知机制

当商户或应用信息发生变更时，系统通过消息队列（MQ）实现数据的异步通知和同步。

### 核心消息类型

1.  **`ResetIsvMchAppInfoConfigMQ`**：
    *   **用途**：广播式消息，用于通知所有节点（如 `sys-payment`）商户或应用的配置信息已更新，需要刷新本地缓存。
    *   **触发时机**：在更新商户信息（`update`）或更新应用信息（`update`）成功后发送。
    *   **消息内容**：包含 `resetType`（2-商户信息，3-应用信息）、`mchNo` 和 `appId`。

```mermaid
sequenceDiagram
participant 代理商 as 代理商系统
participant MchInfoController as MchInfoController
participant MQSender as IMQSender
participant MQBroker as MQ Broker
participant PaymentNode1 as 支付节点1
participant PaymentNode2 as 支付节点2
代理商->>MchInfoController : 更新商户状态
MchInfoController->>MchInfoController : 更新数据库
MchInfoController->>MQSender : send(ResetIsvMchAppInfoConfigMQ)
MQSender->>MQBroker : 发送广播消息
MQBroker->>PaymentNode1 : 接收消息
MQBroker->>PaymentNode2 : 接收消息
PaymentNode1->>PaymentNode1 : 刷新商户配置缓存
PaymentNode2->>PaymentNode2 : 刷新商户配置缓存
```

**Diagram sources**
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java#L16-L83)
- [MchInfoController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchInfoController.java#L228)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java#L228)

2.  **`CleanMchLoginAuthCacheMQ`**：
    *   **用途**：点对点消息，用于清除指定用户在Redis中的登录认证缓存。
    *   **触发时机**：在删除商户或禁用商户时，需要清除该商户所有用户的登录状态。
    *   **消息内容**：包含一个用户ID列表（`userIdList`）。

**Section sources**
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java#L16-L83)
- [CleanMchLoginAuthCacheMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\CleanMchLoginAuthCacheMQ.java#L18-L70)

## 批量管理场景与实现

虽然当前API主要面向单个商户的操作，但可以通过循环调用API来实现批量管理。例如，代理商可以批量为多个商户启用或停用某个支付方式。

**实现示例（伪代码）**：
```java
// 批量更新多个商户的应用状态
List<String> mchNoList = getTargetMchNos(); // 获取目标商户列表
String appId = "APP123";
Byte newState = CS.YES;

for (String mchNo : mchNoList) {
    // 1. 验证当前代理商是否管理该商户
    if (!agentMchRelation