# 转账失败重发

<cite>
**本文档引用文件**   
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java)
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java)
</cite>

## 目录
1. [转账订单重发机制概述](#转账订单重发机制概述)
2. [定时任务扫描逻辑](#定时任务扫描逻辑)
3. [重发服务核心处理逻辑](#重发服务核心处理逻辑)
4. [消息队列在重发中的作用](#消息队列在重发中的作用)
5. [异常处理与最终一致性保障](#异常处理与最终一致性保障)
6. [任务监控与执行指标](#任务监控与执行指标)

## 转账订单重发机制概述

转账失败自动重发机制是支付系统中保障交易最终一致性的关键组件。当转账订单因网络波动、第三方服务异常等原因处于“转账中”状态且长时间未更新时，系统通过定时任务自动触发重发流程，查询上游渠道的真实交易状态，并根据查询结果更新本地订单状态，确保商户和用户能够及时获知最终交易结果。

该机制主要由三个核心组件构成：`TransferOrderReissueTask` 定时任务负责周期性扫描待处理订单；`TransferOrderReissueService` 服务负责执行具体的查询和状态更新逻辑；`PayOrderReissueMQ` 消息队列则用于在系统内部传递重发指令，实现解耦和异步处理。

**Section sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L21-L67)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L19-L141)
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java#L16-L71)

## 定时任务扫描逻辑

`TransferOrderReissueTask` 是一个基于 Spring 的定时任务，通过 `@Scheduled(cron="0 0/1 * * * ?")` 注解配置为每分钟执行一次。其核心职责是扫描数据库中所有需要进行状态补单（Reissue）的转账订单。

### 查询条件
任务使用 `LambdaQueryWrapper` 构造查询条件，筛选出符合以下两个条件的转账订单：
1.  **状态为“转账中”**：`eq(TransferOrder::getState, TransferOrder.STATE_ING)`。这确保了只处理那些已经发起但尚未收到最终确认的订单。
2.  **创建时间在一天内**：`ge(TransferOrder::getCreatedAt, DateUtil.offsetDay(new Date(), -1))`。这是一个重要的限制，避免了对历史过久的订单进行无效查询，防止系统资源浪费。

### 分页处理与执行策略
由于可能存在大量待处理订单，任务采用了分页查询机制，每页查询100条记录（`QUERY_PAGE_SIZE = 100`）。它通过一个 `while(true)` 循环，从第一页开始，逐页获取数据。对于每一页中的每一条订单记录，都会调用 `TransferOrderReissueService.processOrder()` 方法进行处理。循环会在以下任一条件满足时退出：
- 当前页查询结果为空。
- 已经查询到的页码数达到了分页查询的总页数上限。
- 在执行过程中捕获到异常，为防止死循环，任务会立即退出并记录错误日志。

```mermaid
flowchart TD
A[定时任务启动] --> B{查询条件}
B --> C[状态: 转账中]
B --> D[创建时间: 近24小时]
C --> E[分页查询订单]
D --> E
E --> F{查询结果为空?}
F --> |是| G[结束任务]
F --> |否| H[遍历订单列表]
H --> I[调用重发服务处理]
I --> J{是否到达最后一页?}
J --> |否| K[页码+1]
K --> E
J --> |是| G
F --> L{发生异常?}
L --> |是| M[记录错误日志]
M --> G
```

**Diagram sources **
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L30-L63)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java#L22-L211)

**Section sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L30-L63)

## 重发服务核心处理逻辑

`TransferOrderReissueService` 是重发机制的核心业务逻辑实现，其 `processOrder` 方法负责处理单个转账订单的状态查询和更新。

### 核心处理流程
1.  **获取订单信息**：方法接收一个 `TransferOrder` 对象作为参数，从中提取 `transferId`（转账订单号）和 `ifCode`（支付接口代码）。
2.  **查找支付接口实现**：利用 `SpringBeansUtil.getBean()` 方法，根据 `ifCode` 动态获取对应的 `ITransferService` 实现类（如 `AlipayTransferService` 或 `WxpayTransferService`）。如果找不到对应的实现，则记录错误并返回。
3.  **获取商户配置**：调用 `ConfigContextQueryService.queryMchInfoAndAppInfo()` 方法，根据商户号（`mchNo`）和应用ID（`appId`）查询并构建 `MchAppConfigContext` 对象，该对象包含了商户的支付参数、密钥等必要信息。
4.  **发起状态查询**：调用上一步获取到的 `ITransferService` 实例的 `query()` 方法，向第三方支付渠道发起订单状态查询请求。该方法返回一个 `ChannelRetMsg` 对象，封装了渠道的响应结果。
5.  **处理查询结果**：根据 `ChannelRetMsg` 中的 `channelState` 字段，判断订单的最终状态：
    -   **明确成功** (`CONFIRM_SUCCESS`)：调用 `transferOrderService.updateIng2Success()` 将本地订单状态更新为“转账成功”，并触发商户通知。
    -   **明确失败** (`CONFIRM_FAIL`)：调用 `transferOrderService.updateIng2Fail()` 将本地订单状态更新为“转账失败”，并触发商户通知。
    -   **其他状态**（如处理中、未知等）：订单状态保持不变，等待下一次任务扫描。

```mermaid
sequenceDiagram
participant Task as TransferOrderReissueTask
participant Service as TransferOrderReissueService
participant Spring as SpringBeansUtil
participant Config as ConfigContextQueryService
participant Channel as ITransferService
participant DB as TransferOrderService
Task->>Service : processOrder(transferOrder)
Service->>Spring : getBean(ifCode + "TransferService")
alt 接口存在
Service->>Config : queryMchInfoAndAppInfo(mchNo, appId)
Config-->>Service : MchAppConfigContext
Service->>Channel : query(transferOrder, context)
Channel-->>Service : ChannelRetMsg
alt channelState == CONFIRM_SUCCESS
Service->>DB : updateIng2Success()
Service->>PayMchNotifyService : transferOrderNotify()
else channelState == CONFIRM_FAIL
Service->>DB : updateIng2Fail()
Service->>PayMchNotifyService : transferOrderNotify()
else 其他状态
Note over Service : 订单状态保持不变
end
else 接口不存在
Service->>Service : log.error() and return
end
```

**Diagram sources **
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L29-L74)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java#L55-L77)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L13-L108)

**Section sources**
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L29-L74)

## 消息队列在重发中的作用

虽然 `TransferOrderReissueTask` 直接调用 `TransferOrderReissueService`，但系统中存在 `PayOrderReissueMQ` 消息模型，这表明重发机制的设计支持消息驱动的异步处理模式，为未来的扩展和解耦提供了基础。

### PayOrderReissueMQ 消息结构
`PayOrderReissueMQ` 是一个继承自 `AbstractMQ` 的消息模型，其核心是内部类 `MsgPayload`，包含两个字段：
-   **payOrderId**：支付订单号。尽管名为“支付订单号”，但在实际应用中，此字段可用于传递“转账订单号”（`transferId`），作为重发指令的唯一标识。
-   **count**：通知次数。可用于记录该订单已被重发的次数，为实现重试次数限制提供依据。

### 消息的作用与流程
1.  **解耦**：通过消息队列，可以将“订单扫描”和“订单处理”两个环节解耦。扫描任务只需将需要重发的订单号发送到消息队列，而具体的查询和更新操作由独立的消费者服务来完成，提高了系统的可维护性和可扩展性。
2.  **异步处理**：重发操作可能涉及网络IO，耗时较长。使用消息队列可以实现异步处理，避免阻塞定时任务的执行，保证任务的准时性。
3.  **可靠传递**：消息队列通常具备持久化和重试机制，即使消费者服务暂时不可用，消息也不会丢失，确保了重发指令的可靠传递。

```mermaid
erDiagram
PayOrderReissueMQ ||--o{ MsgPayload : contains
class PayOrderReissueMQ {
+MQ_NAME: String
+getMQName(): String
+getMQType(): MQSendTypeEnum
+toMessage(): String
+build(payOrderId, count): PayOrderReissueMQ
}
class MsgPayload {
+payOrderId: String
+count: Integer
}
```

**Diagram sources **
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java#L16-L71)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java#L9-L20)

**Section sources**
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java#L16-L71)

## 异常处理与最终一致性保障

重发机制通过多层次的异常处理和状态更新策略，确保了系统的健壮性和交易的最终一致性。

### 异常处理策略
1.  **任务层异常**：在 `TransferOrderReissueTask` 的 `while` 循环中，使用 `try-catch` 捕获所有异常。一旦发生任何异常，任务会立即记录错误日志并退出本次执行，防止因单个订单的异常导致整个任务陷入死循环。
2.  **服务层异常**：在 `TransferOrderReissueService.processOrder()` 方法中，同样使用 `try-catch` 捕获异常。当发生异常时，方法会记录详细的错误日志（包括 `transferId`），并返回 `null`。这使得任务可以跳过当前订单，继续处理列表中的下一个订单，保证了整体流程的连续性。
3.  **业务逻辑异常**：在 `updateInitOrderStateThrowException` 等方法中，当数据库更新操作失败时，会抛出 `BizException`。这种设计确保了状态更新的原子性，如果第一步更新失败，整个流程会中断，避免了数据不一致。

### 最终一致性保障
1.  **幂等性设计**：所有状态更新方法（如 `updateIng2Success`）都基于订单号和当前状态进行条件更新（`eq(TransferOrder::getTransferId, transferId).eq(TransferOrder::getState, TransferOrder.STATE_ING)`）。这意味着即使同一条订单被多次处理，也只会成功更新一次，保证了操作的幂等性。
2.  **状态机驱动**：系统通过 `TransferOrder` 的 `state` 字段（0-生成, 1-转账中, 2-成功, 3-失败）严格管理订单的生命周期。重发任务只处理“转账中”状态的订单，并将其推向“成功”或“失败”的终态，确保了状态流转的正确性。
3.  **闭环通知**：当订单状态更新为“成功”或“失败”后，系统会立即调用 `PayMchNotifyService.transferOrderNotify()`，将结果通过异步通知的方式告知商户。这确保了商户端也能及时感知到交易的最终结果，完成了整个交易闭环。

```mermaid
stateDiagram-v2
[*] --> 扫描订单
扫描订单 --> 查询订单 : 每分钟
查询订单 --> 处理订单 : 状态=转账中
处理订单 --> 查询渠道
查询渠道 --> 更新状态
更新状态 --> 通知商户 : 成功/失败
更新状态 --> 保持状态 : 处理中/未知
通知商户 --> [*]
保持状态 --> 扫描订单 : 下次任务
```

**Section sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L50-L60)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L120-L138)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java#L41-L57)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L138-L185)

## 任务监控与执行指标

为了确保重发任务的稳定运行和及时发现问题，需要建立有效的监控体系。

### 关键监控指标
1.  **任务执行频率**：监控 `TransferOrderReissueTask` 是否严格按照每分钟执行一次。可以通过日志中的执行时间戳来验证。
2.  **每次处理订单数**：统计每次任务执行时扫描到的“转账中”订单数量。如果该数值持续增长，可能意味着上游渠道响应缓慢或存在大量失败订单，需要排查。
3.  **任务执行时长**：监控单次任务执行的总耗时。过长的执行时间可能影响其他定时任务的调度，需要优化查询或处理逻辑。
4.  **异常日志数量**：监控 `TransferOrderReissueTask` 和 `TransferOrderReissueService` 中记录的错误日志（`log.error`）数量。任何异常都应被及时告警和处理。
5.  **状态更新成功率**：统计在重发任务中，成功将“转账中”订单更新为“成功”或“失败”状态的比例。低成功率可能表明渠道查询接口存在问题。

### 监控方法
-   **日志分析**：通过 ELK（Elasticsearch, Logstash, Kibana）或类似日志系统，收集和分析 `sys-payment` 服务的日志，重点关注 `TransferOrderReissueTask` 和 `TransferOrderReissueService` 的日志输出。
-   **应用性能监控（APM）**：集成 SkyWalking、Prometheus 等 APM 工具，对 `processOrder` 方法进行埋点，监控其调用次数、响应时间和错误率。
-   **数据库监控**：监控 `t_transfer_order` 表中 `state=1`（转账中）的记录数量，设置告警阈值，当数量超过预设值时发出告警。

**Section sources**
- [TransferOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/TransferOrderReissueTask.java#L50-L60)
- [TransferOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/TransferOrderReissueService.java#L29-L74)