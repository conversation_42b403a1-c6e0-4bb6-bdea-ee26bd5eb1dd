# 技术栈与依赖

<cite>
**本文档中引用的文件**  
- [pom.xml](file://pom.xml)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)
- [application.yml](file://sys-agent/src/main/resources/application.yml)
- [application.yml](file://sys-manager/src/main/resources/application.yml)
- [application.yml](file://sys-merchant/src/main/resources/application.yml)
- [application.yml](file://sys-payment/src/main/resources/application.yml)
- [package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-merchant/package.json)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [后端技术栈](#后端技术栈)
4. [前端技术栈](#前端技术栈)
5. [构建与依赖管理](#构建与依赖管理)
6. [微服务架构支持](#微服务架构支持)
7. [配置示例与集成](#配置示例与集成)
8. [高级话题](#高级话题)
9. [结论](#结论)

## 简介
本项目“四方支付系统”采用现代化的技术栈，结合Spring Boot、MyBatis、Redis和多种消息队列（ActiveMQ/RabbitMQ/RocketMQ）构建高性能、可扩展的后端服务。前端采用Vue.js、Vite和TypeScript实现响应式用户界面。系统通过Maven和npm/yarn分别管理Java和JavaScript依赖，支持微服务架构和前后端分离模式。本文档详细说明各技术选型原因、集成方式及实际配置。

## 项目结构
项目采用模块化设计，包含多个子模块，每个模块负责特定功能。整体结构清晰，便于维护和扩展。

```mermaid
graph TD
A[UniPay] --> B[code-gen]
A --> C[core]
A --> D[components]
A --> E[service]
A --> F[sys-manager]
A --> G[sys-merchant]
A --> H[sys-agent]
A --> I[sys-payment]
A --> J[unipay-web-ui]
D --> K[components-mq]
D --> L[components-oss]
J --> M[unipay-ui-agent]
J --> N[unipay-ui-manager]
J --> O[unipay-ui-merchant]
```

**图示来源**  
- [pom.xml](file://pom.xml)

**本节来源**  
- [pom.xml](file://pom.xml)

## 后端技术栈

### Spring Boot
Spring Boot作为核心框架，提供自动配置、内嵌服务器和生产级监控功能。项目基于Spring Boot 3.3.7版本，确保了稳定性和安全性。各子系统（如运营平台、商户平台、支付网关）均以独立的Spring Boot应用运行，通过`spring-boot-starter-parent`统一管理依赖版本。

### MyBatis
MyBatis作为持久层框架，结合MyBatis Plus 3.5.7增强版，简化了数据库操作。通过XML映射文件（如`Mapper.xml`）和注解方式实现SQL与Java代码的解耦，提高了开发效率和可维护性。

### Redis
Redis用于缓存高频访问数据，提升系统性能。项目中通过`RedisUtil`工具类封装了Redis操作，支持字符串和对象的存取、过期时间设置、键值查询等功能。特别地，`RedisUtil`实现了重试机制，在网络波动时保障缓存操作的可靠性。

```mermaid
classDiagram
class RedisUtil {
+static String getString(String key)
+static <T> T getObject(String key, Class<T> cls)
+static void setString(String key, String value)
+static void set(String key, Object value)
+static void expire(String key, long time)
+static boolean hasKey(String key)
+static void del(String... key)
+static Collection<String> keys(String pattern)
-static RedisTemplate getStringRedisTemplate()
-static <T> T executeWithRetry(Supplier<T> operation, int maxRetries)
}
```

**图示来源**  
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)

**本节来源**  
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)

### 消息队列
项目支持多种消息队列中间件，包括ActiveMQ、RabbitMQ、RocketMQ和阿里云RocketMQ。通过`components-mq`模块抽象出统一的发送和接收接口，实现厂商解耦。

#### 技术选型原因
- **ActiveMQ**：轻量级、易于部署，适合中小型系统。
- **RabbitMQ**：高可靠性、丰富的路由策略，适用于复杂业务场景。
- **RocketMQ**：高吞吐量、分布式事务支持，适合大规模分布式系统。
- **阿里云RocketMQ**：云原生集成，降低运维成本。

#### 集成方式
通过`IMQSender`和`IMQMsgReceiver`接口定义消息的发送与接收行为，具体实现由各厂商提供。在`application.yml`中通过`isys.mq.vender`配置项动态切换消息队列厂商。

```mermaid
classDiagram
class IMQSender {
+void send(AbstractMQ mqModel)
+void send(AbstractMQ mqModel, int delay)
}
class IMQMsgReceiver {
+void receiveMsg(String msg)
}
IMQSender <|.. ActiveMQSender
IMQSender <|.. RabbitMQSender
IMQSender <|.. RocketMQSender
IMQMsgReceiver <|.. ActiveMQReceiver
IMQMsgReceiver <|.. RabbitMQReceiver
IMQMsgReceiver <|.. RocketMQReceiver
```

**图示来源**  
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)

**本节来源**  
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)

## 前端技术栈

### Vue.js
Vue.js作为前端框架，采用组件化开发模式，提升代码复用性和可维护性。项目中包含多个前端子应用（如代理商、运营平台、商户平台），均基于Vue 3构建，利用Composition API提高逻辑组织能力。

### Vite
Vite作为构建工具，提供极速的冷启动和热更新体验。相比传统Webpack，Vite基于ES模块原生支持，显著提升了开发效率。配置文件`vite.config.ts`中定义了别名、插件和构建选项。

### TypeScript
TypeScript增强了代码的可读性和健壮性，通过静态类型检查减少运行时错误。项目中使用`.ts`和`.vue`文件，结合`tsconfig.json`进行类型配置，确保类型安全。

#### 开发体验优势
- **快速启动**：Vite的开发服务器启动时间极短。
- **热模块替换**：修改代码后即时预览，无需刷新页面。
- **类型安全**：TypeScript提供智能提示和错误检测。
- **生态丰富**：Ant Design Vue等UI库提供高质量组件。

**本节来源**  
- [package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-merchant/package.json)

## 构建与依赖管理

### Maven
Maven作为Java项目的构建工具，通过`pom.xml`文件管理依赖和构建生命周期。项目采用多模块结构，顶层`pom.xml`定义了统一的版本号、Java版本（17）和依赖管理。

#### 关键依赖引入
- **Spring Boot Starter Parent**：继承Spring Boot的默认配置。
- **MyBatis Plus Starter**：简化MyBatis配置。
- **FastJSON**：高性能JSON处理。
- **Hutool**：Java工具包，提供常用工具方法。
- **Knife4j**：API文档生成工具，替代Swagger。

### npm/yarn
npm和yarn用于管理前端依赖。项目中使用`package.json`定义依赖项和脚本命令。推荐使用yarn以获得更好的依赖锁定和安装速度。

#### 包管理器使用
- **开发依赖**：TypeScript、Vite插件、ESLint等。
- **生产依赖**：Vue、Ant Design Vue、Axios等。
- **脚本命令**：`dev`启动开发服务器，`build`生成生产包。

**本节来源**  
- [pom.xml](file://pom.xml)
- [package.json](file://unipay-web-ui/unipay-ui-agent/package.json)

## 微服务架构支持
项目通过多个独立的Spring Boot应用实现微服务架构：
- **sys-manager**：运营平台管理端
- **sys-merchant**：商户平台管理端
- **sys-agent**：代理商平台管理端
- **sys-payment**：支付统一网关

各服务通过RESTful API通信，前端通过Nginx反向代理实现统一入口。消息队列用于服务间异步通信，如配置更新广播、订单状态通知等。

## 配置示例与集成

### 后端配置
在`application.yml`中配置消息队列厂商：

```yaml
isys:
  mq:
    vender: activeMQ  # 可选 activeMQ, rabbitMQ, rocketMQ, aliYunRocketMQ
```

此配置决定了运行时使用的MQ实现，需确保`components-mq/pom.xml`中包含对应依赖。

### 前端配置
`vite.config.ts`中配置路径别名和代理：

```typescript
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    proxy: {
      '/api': 'http://localhost:9216'
    }
  }
})
```

## 高级话题

### 版本兼容性
- **Spring Boot 3.3.7** 要求Java 17，确保JDK版本匹配。
- **MyBatis Plus 3.5.7** 兼容Spring Boot 3.x。
- **Vue 3.2.21** 与Vite 2.6.13兼容。

### 依赖冲突解决
- 使用`dependencyManagement`统一管理版本，避免传递性依赖冲突。
- 排除不必要的传递依赖，如：
  ```xml
  <exclusions>
    <exclusion>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-logging</artifactId>
    </exclusion>
  </exclusions>
  ```
- 定期运行`mvn dependency:tree`分析依赖树。

### 缓存与消息一致性
当启用`cache-config: true`时，需确保MQ广播模式正常工作，以同步各节点的缓存状态。否则可能导致配置不一致。

## 结论
本项目技术栈选型合理，兼顾性能、可维护性和扩展性。后端采用Spring Boot + MyBatis + Redis + 多MQ方案，支持高并发和分布式部署。前端使用Vue.js + Vite + TypeScript，提供现代化开发体验。通过Maven和npm/yarn有效管理依赖，支持微服务架构和前后端分离。建议生产环境根据实际需求选择合适的消息队列厂商，并合理配置缓存策略。