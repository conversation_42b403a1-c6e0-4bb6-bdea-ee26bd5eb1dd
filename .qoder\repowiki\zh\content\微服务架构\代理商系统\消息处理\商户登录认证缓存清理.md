# 商户登录认证缓存清理

<cite>
**本文档引用文件**   
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [AuthService.java](file://sys-merchant/src/main/java/com/unipay/mch/service/AuthService.java)
- [AuthService.java](file://sys-agent/src/main/java/com/unipay/agent/service/AuthService.java)
- [AuthService.java](file://sys-manager/src/main/java/com/unipay/mgr/service/AuthService.java)
</cite>

## 目录
1. [引言](#引言)
2. [消息产生场景与业务意义](#消息产生场景与业务意义)
3. [消息结构与序列化](#消息结构与序列化)
4. [消息监听与处理机制](#消息监听与处理机制)
5. [缓存清理流程详解](#缓存清理流程详解)
6. [异常处理与重试机制](#异常处理与重试机制)
7. [分布式缓存同步实现](#分布式缓存同步实现)
8. [总结](#总结)

## 引言

在统一支付系统中，商户登录认证信息的缓存管理是保障系统安全性和性能的关键环节。当商户用户信息发生变更或权限被撤销时，必须及时清理其在Redis中的登录认证缓存，以防止已失效的用户继续访问系统。本文档详细阐述了通过消息队列实现的商户登录认证缓存清理机制，包括消息的产生、传输、监听和处理全过程。

**本文档引用文件**   
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)

## 消息产生场景与业务意义

`CleanMchLoginAuthCacheMQ`消息的产生主要发生在以下业务场景：

1. **用户权限变更**：当商户管理员修改用户权限或禁用用户账号时
2. **用户信息更新**：当用户基本信息（如状态、角色）发生变更时
3. **系统配置重置**：当系统进行全局配置重置操作时
4. **安全策略调整**：当系统安全策略发生变更需要强制用户重新登录时

该消息的业务意义在于：
- **保障系统安全**：确保已失效或权限变更的用户无法继续使用旧的认证信息访问系统
- **维护数据一致性**：在分布式系统中保持各节点缓存数据的一致性
- **提升用户体验**：强制用户重新登录以获取最新的权限信息，避免权限错乱
- **降低安全风险**：及时清理无效会话，减少潜在的安全漏洞

消息采用点对点（QUEUE）的传输模式，确保消息被可靠地传递到目标消费者。

**本文档引用文件**   
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [AuthService.java](file://sys-merchant/src/main/java/com/unipay/mch/service/AuthService.java)
- [AuthService.java](file://sys-agent/src/main/java/com/unipay/agent/service/AuthService.java)

## 消息结构与序列化

`CleanMchLoginAuthCacheMQ`消息的结构设计遵循统一的消息模型规范，继承自`AbstractMQ`抽象类。

### 消息核心属性

```mermaid
classDiagram
class CleanMchLoginAuthCacheMQ {
+String MQ_NAME
-MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(List<Long>) CleanMchLoginAuthCacheMQ
+parse(String) MsgPayload
}
class MsgPayload {
-List<Long> userIdList
}
CleanMchLoginAuthCacheMQ --> MsgPayload : "包含"
CleanMchLoginAuthCacheMQ --|> AbstractMQ : "继承"
```

**图示来源**  
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java#L18-L70)

### 消息序列化过程

消息的序列化和反序列化通过以下方法实现：

- **构建消息**：`build(List<Long> userIdList)`方法接收用户ID列表，创建包含`MsgPayload`的消息实例
- **序列化**：`toMessage()`方法将`MsgPayload`对象转换为JSON字符串，便于网络传输
- **反序列化**：`parse(String msg)`方法将接收到的JSON字符串解析为`MsgPayload`对象

消息名称定义为`QUEUE_CLEAN_MCH_LOGIN_AUTH_CACHE`，采用点对点的传输模式，确保消息的可靠传递。

**本文档引用文件**   
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java#L18-L70)

## 消息监听与处理机制

系统在多个服务模块中实现了`CleanMchLoginAuthCacheMQReceiver`消息监听器，确保跨系统的缓存同步。

### 监听器部署

消息监听器在以下系统模块中均有部署：
- **商户系统**（sys-merchant）
- **代理商系统**（sys-agent）
- **运营平台**（sys-manager）

这种多点监听的设计确保了在分布式架构下，所有相关系统的缓存都能得到同步清理。

### 消息处理流程

```mermaid
sequenceDiagram
participant Producer as "消息生产者"
participant MQ as "消息队列"
participant Consumer as "消息消费者"
Producer->>MQ : 发送CleanMchLoginAuthCacheMQ消息
MQ->>Consumer : 推送消息
Consumer->>Consumer : receive(MsgPayload payload)
Consumer->>RedisUtil : keys(缓存键模式)
RedisUtil-->>Consumer : 返回缓存键列表
loop 遍历每个缓存键
Consumer->>RedisUtil : del(缓存键)
end
Consumer->>Producer : 处理完成隐式
```

**图示来源**  
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java#L18-L46)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java#L18-L46)

**本文档引用文件**   
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java)

## 缓存清理流程详解

缓存清理流程是本机制的核心，涉及缓存键的构建、查询和删除操作。

### 缓存键构建

系统通过`CS.getCacheKeyToken(sysUserId, "*")`方法构建缓存键的查询模式：
- **键格式**：`TOKEN_%s_%s`，其中第一个`%s`为用户ID，第二个`%s`为UUID
- **通配符使用**：使用`*`通配符匹配特定用户的所有会话缓存

### 缓存清理步骤

```mermaid
flowchart TD
Start([开始]) --> CheckPayload["检查消息负载"]
CheckPayload --> PayloadValid{"负载有效?"}
PayloadValid --> |否| LogEmpty["记录空用户ID日志"]
PayloadValid --> |是| LoopUser["遍历用户ID列表"]
LoopUser --> GetCacheKeys["查询用户缓存键"]
GetCacheKeys --> KeysExist{"存在缓存键?"}
KeysExist --> |否| ContinueLoop["继续下一个用户"]
KeysExist --> |是| LoopKey["遍历每个缓存键"]
LoopKey --> DeleteCache["删除Redis缓存"]
DeleteCache --> ContinueKey["继续下一个缓存键"]
ContinueKey --> EndLoopKey["结束缓存键循环"]
EndLoopKey --> ContinueLoop
ContinueLoop --> EndLoopUser["结束用户循环"]
EndLoopUser --> LogClear["记录清理完成日志"]
LogClear --> End([结束])
```

**图示来源**  
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java#L22-L45)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java#L152-L154)

### 关键方法说明

- **RedisUtil.keys(pattern)**：根据模式查询匹配的缓存键，返回`Collection<String>`
- **RedisUtil.del(key)**：删除指定的缓存键，支持单个或多个键的删除
- **CS.getCacheKeyToken()**：根据用户ID和UUID生成标准的缓存键

**本文档引用文件**   
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java#L22-L45)
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java#L141-L154)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java#L200-L204)

## 异常处理与重试机制

系统在缓存操作层面实现了完善的异常处理和重试机制，确保操作的可靠性。

### Redis操作重试

`RedisUtil`工具类中的`executeWithRetry`方法提供了通用的重试机制：

```mermaid
sequenceDiagram
participant Operation as "Redis操作"
participant Retry as "重试机制"
participant Logger as "日志系统"
Operation->>Retry : 执行操作
alt 操作成功
Retry-->>Operation : 返回结果
else 操作失败
Retry->>Logger : 记录警告日志
Retry->>Retry : 等待递增时间
retry 重试次数 < 最大重试次数
Retry->>Operation : 重新执行操作
end
Retry->>Logger : 记录错误日志
Retry->>Operation : 抛出运行时异常
end
```

**图示来源**  
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java#L39-L60)

### 重试机制特点

- **最大重试次数**：3次
- **递增等待时间**：100ms、200ms、300ms，避免雪崩效应
- **异常处理**：捕获所有异常，记录详细的警告和错误日志
- **最终失败**：重试失败后抛出运行时异常，触发上层的错误处理

该机制确保了在Redis临时不可用或网络波动的情况下，缓存操作仍能最终成功。

**本文档引用文件**   
- [RedisUtil.java](file://core/src/main/java/com/unipay/core/cache/RedisUtil.java#L39-L60)

## 分布式缓存同步实现

通过消息队列实现的缓存清理机制，有效解决了分布式系统中的缓存一致性问题。

### 架构优势

1. **解耦设计**：消息生产者与消费者完全解耦，互不影响
2. **异步处理**：缓存清理操作异步执行，不影响主业务流程性能
3. **可靠传递**：消息队列保证消息的可靠传递，即使消费者暂时不可用
4. **水平扩展**：可轻松增加新的消费者，适应系统扩展需求

### 同步流程

当用户权限变更时：
1. 业务服务调用`delAuthentication`或`refAuthentication`方法
2. 方法内部构建`CleanMchLoginAuthCacheMQ`消息并发送到消息队列
3. 所有在线的系统实例（商户、代理、管理）同时接收到消息
4. 各实例并行执行缓存清理操作
5. 完成跨系统的缓存同步

这种机制确保了在多节点部署环境下，所有节点的缓存数据保持一致，避免了因缓存不一致导致的安全问题。

**本文档引用文件**   
- [AuthService.java](file://sys-merchant/src/main/java/com/unipay/mch/service/AuthService.java#L168-L181)
- [AuthService.java](file://sys-agent/src/main/java/com/unipay/agent/service/AuthService.java#L167-L180)
- [AuthService.java](file://sys-manager/src/main/java/com/unipay/mgr/service/AuthService.java#L151-L164)

## 总结

商户登录认证缓存清理机制通过消息队列实现了高效、可靠的分布式缓存同步。该机制具有以下特点：

- **高可靠性**：基于消息队列的可靠传递机制和Redis操作的重试机制
- **高一致性**：确保多节点部署环境下缓存数据的一致性
- **高安全性**：及时清理失效的用户会话，保障系统安全
- **高可扩展性**：解耦的设计便于系统扩展和维护

通过`CleanMchLoginAuthCacheMQ`消息的标准化设计和`CleanMchLoginAuthCacheMQReceiver`的广泛部署，系统实现了跨服务的缓存同步，为统一支付平台的稳定运行提供了有力保障。