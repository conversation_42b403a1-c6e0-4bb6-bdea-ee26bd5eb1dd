# 商户系统

<cite>
**本文档引用的文件**   
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java)
- [MchInfo.java](file://core\src\main\java\com\unipay\core\entity\MchInfo.java)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java)
- [PayInterfaceConfig.java](file://core\src\main\java\com\unipay\core\entity\PayInterfaceConfig.java)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java)
- [MchNotifyRecordService.java](file://service\src\main\java\com\unipay\service\impl\MchNotifyRecordService.java)
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java)
- [ConfigContextQueryService.java](file://sys-payment\src\main\java\com\unipay\pay\service\ConfigContextQueryService.java)
- [MchInfoService.java](file://service\src\main\java\com\unipay\service\impl\MchInfoService.java)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java)
- [MchPayPassageService.java](file://service\src\main\java\com\unipay\service\impl\MchPayPassageService.java)
</cite>

## 目录
1. [商户系统概述](#商户系统概述)
2. [核心业务功能](#核心业务功能)
   - [商户信息管理](#商户信息管理)
   - [应用管理](#应用管理)
   - [支付通道配置](#支付通道配置)
   - [订单管理](#订单管理)
3. [支付结果通知处理](#支付结果通知处理)
4. [系统集成](#系统集成)
   - [与核心模块集成](#与核心模块集成)
   - [与消息队列集成](#与消息队列集成)
5. [开发者最佳实践](#开发者最佳实践)
   - [业务逻辑处理](#业务逻辑处理)
   - [密钥与配置安全](#密钥与配置安全)

## 商户系统概述

商户系统是统一支付平台的核心组成部分，为商户提供全面的支付服务管理功能。该系统通过模块化设计，实现了商户信息、应用、支付通道和订单的全生命周期管理。系统采用微服务架构，通过消息队列实现异步通信，确保高并发场景下的稳定性和可靠性。商户系统与核心模块紧密集成，通过统一的接口规范和数据模型，实现了业务逻辑的高效处理和数据的一致性。

**Section sources**
- [MchInfo.java](file://core\src\main\java\com\unipay\core\entity\MchInfo.java#L24-L140)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)

## 核心业务功能

### 商户信息管理

商户信息管理功能允许系统管理员创建、更新和删除商户基本信息。每个商户由唯一的商户号（mchNo）标识，包含商户名称、联系人信息、状态等关键属性。系统支持两种商户类型：普通商户（TYPE_NORMAL）和特约商户（TYPE_ISVSUB）。特约商户模式下，商户隶属于特定的服务商（isvNo），适用于服务商代理场景。商户状态（state）用于控制商户的启用和停用，确保业务合规性。

```mermaid
classDiagram
class MchInfo {
+String mchNo
+String mchName
+String mchShortName
+Byte type
+String isvNo
+String agentNo
+String contactName
+String contactTel
+String contactEmail
+Byte state
+String remark
+Long initUserId
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [MchInfo.java](file://core\src\main\java\com\unipay\core\entity\MchInfo.java#L24-L140)

**Section sources**
- [MchInfo.java](file://core\src\main\java\com\unipay\core\entity\MchInfo.java#L24-L140)
- [MchInfoService.java](file://service\src\main\java\com\unipay\service\impl\MchInfoService.java#L29-L166)

### 应用管理

应用管理功能为每个商户提供独立的应用实例，实现业务隔离和权限控制。每个应用由唯一的应用ID（appId）标识，关联到特定的商户号（mchNo）。应用包含应用名称、状态和应用私钥（appSecret）等核心属性。应用私钥是商户与系统通信的安全凭证，用于API请求的签名验证。系统支持为商户创建默认应用，并提供应用的增删改查操作，确保商户能够灵活管理其应用实例。

```mermaid
classDiagram
class MchApp {
+String appId
+String appName
+String mchNo
+Byte state
+String appSecret
+String remark
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)

**Section sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L27-L96)

### 支付通道配置

支付通道配置功能允许商户为每个应用配置可用的支付接口和支付方式。通过`MchPayPassage`实体，系统将商户、应用、支付接口（ifCode）和支付方式（wayCode）进行关联，并设置相应的费率（rate）和状态（state）。商户可以启用或禁用特定的支付通道，实现灵活的支付策略管理。系统通过`MchPayPassageService`提供查询和保存接口，支持批量操作和状态管理，确保配置的准确性和一致性。

```mermaid
classDiagram
class MchPayPassage {
+Long id
+String mchNo
+String appId
+String ifCode
+String wayCode
+BigDecimal rate
+String riskConfig
+Byte state
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)

**Section sources**
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)
- [MchPayPassageService.java](file://service\src\main\java\com\unipay\service\impl\MchPayPassageService.java#L29-L124)

### 订单管理

订单管理功能是商户系统的核心，负责处理支付订单的全生命周期。`PayOrder`实体包含支付订单的所有关键信息，如支付订单号（payOrderId）、商户订单号（mchOrderNo）、支付金额（amount）、支付状态（state）等。系统支持多种支付状态，包括订单生成（STATE_INIT）、支付中（STATE_ING）、支付成功（STATE_SUCCESS）和支付失败（STATE_FAIL）。订单管理服务提供查询、更新和统计功能，支持按商户号、应用ID、时间范围等条件进行筛选，满足商户的多样化需求。

```mermaid
classDiagram
class PayOrder {
+String payOrderId
+String mchNo
+String isvNo
+String appId
+String mchName
+Byte mchType
+String mchOrderNo
+String ifCode
+String wayCode
+Long amount
+BigDecimal mchFeeRate
+Long mchFeeAmount
+String currency
+Byte state
+Byte notifyState
+String clientIp
+String subject
+String body
+String channelExtra
+String channelUser
+String channelOrderNo
+Byte refundState
+Integer refundTimes
+Long refundAmount
+Byte divisionMode
+Byte divisionState
+Date divisionLastTime
+String errCode
+String errMsg
+String extParam
+String notifyUrl
+String returnUrl
+Date expiredTime
+Date successTime
+Date createdAt
+Date updatedAt
}
```

**Diagram sources**
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)

**Section sources**
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java#L36-L640)

## 支付结果通知处理

商户系统通过消息队列机制处理支付结果通知，确保通知的可靠性和及时性。当支付订单状态更新为成功或失败时，系统会触发通知流程。`PayMchNotifyService`负责生成通知URL，包含订单信息和签名，然后通过`IMQSender`将通知任务推送到`PayOrderMchNotifyMQ`消息队列。`PayOrderMchNotifyMQReceiver`作为消息消费者，从队列中接收通知任务，向商户的异步通知地址（notifyUrl）发送HTTP请求。通知过程支持最多6次重试，每次重试间隔递增，确保在网络波动等异常情况下仍能成功送达。

```mermaid
sequenceDiagram
participant PayOrderService as PayOrderService
participant PayMchNotifyService as PayMchNotifyService
participant MQSender as IMQSender
participant MQ as PayOrderMchNotifyMQ
participant MQReceiver as PayOrderMchNotifyMQReceiver
participant Merchant as 商户系统
PayOrderService->>PayMchNotifyService : 调用payOrderNotify()
PayMchNotifyService->>PayMchNotifyService : 生成签名通知URL
PayMchNotifyService->>MQSender : 发送通知MQ
MQSender->>MQ : 推送PayOrderMchNotifyMQ
MQ->>MQReceiver : 传递通知消息
MQReceiver->>MQReceiver : 查询通知记录
MQReceiver->>Merchant : 发送HTTP POST通知
alt 通知成功
Merchant-->>MQReceiver : 返回SUCCESS
MQReceiver->>MQReceiver : 更新通知状态为成功
else 通知失败
MQReceiver->>MQSender : 延迟重试发送
MQSender->>MQ : 推送延迟MQ
end
```

**Diagram sources**
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java#L36-L640)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java#L25-L260)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java#L23-L98)

**Section sources**
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java#L23-L98)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java#L25-L260)
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java#L16-L68)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)

## 系统集成

### 与核心模块集成

商户系统与核心模块通过共享的数据模型和业务服务实现深度集成。核心模块提供`MchInfo`、`MchApp`、`PayOrder`等实体类，定义了统一的数据结构和业务规则。商户系统通过依赖注入的方式使用`PayOrderService`、`MchInfoService`等核心服务，实现对商户、应用和订单的管理。`ConfigContextQueryService`作为集成的桥梁，提供缓存和查询功能，优化系统性能。通过这种集成方式，商户系统能够高效地访问和操作核心数据，确保业务逻辑的一致性和数据的完整性。

**Section sources**
- [ConfigContextQueryService.java](file://sys-payment\src\main\java\com\unipay\pay\service\ConfigContextQueryService.java#L29-L199)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java#L36-L640)

### 与消息队列集成

商户系统通过`components-mq`组件与消息队列集成，实现异步通信和解耦。系统定义了`PayOrderMchNotifyMQ`等消息模型，封装通知任务的负载数据。`IMQSender`接口提供实时和延迟发送功能，支持点对点（QUEUE）和广播（BROADCAST）两种模式。`PayOrderMchNotifyMQReceiver`实现`IMQReceiver`接口，作为消息消费者处理通知任务。这种集成方式提高了系统的可扩展性和容错能力，确保在高并发场景下仍能稳定运行。

```mermaid
classDiagram
class PayOrderMchNotifyMQ {
+String MQ_NAME
+MsgPayload payload
+String getMQName()
+MQSendTypeEnum getMQType()
+String toMessage()
+static PayOrderMchNotifyMQ build(Long notifyId)
+static MsgPayload parse(String msg)
+interface IMQReceiver
}
class IMQSender {
+void send(AbstractMQ mqModel)
+void send(AbstractMQ mqModel, int delay)
}
class PayOrderMchNotifyMQReceiver {
+void receive(PayOrderMchNotifyMQ.MsgPayload payload)
}
PayOrderMchNotifyMQReceiver ..|> PayOrderMchNotifyMQ : : IMQReceiver
PayOrderMchNotifyMQReceiver --> IMQSender : 使用
PayOrderMchNotifyMQ --> AbstractMQ : 继承
```

**Diagram sources**
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java#L16-L68)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java#L23-L98)

**Section sources**
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java#L16-L68)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java#L23-L98)

## 开发者最佳实践

### 业务逻辑处理

在处理商户端业务逻辑时，开发者应遵循以下最佳实践：
1. **事务管理**：使用`@Transactional`注解确保数据一致性，特别是在涉及多个数据库操作的场景。
2. **异常处理**：通过`BizException`等自定义异常类处理业务异常，提供清晰的错误信息。
3. **日志记录**：使用`@Slf4j`注解添加日志，记录关键操作和异常信息，便于问题排查。
4. **缓存优化**：利用`ConfigContextQueryService`的缓存功能，减少数据库查询，提高系统性能。
5. **幂等性设计**：确保关键操作（如订单更新、通知发送）的幂等性，避免重复处理导致的数据不一致。

**Section sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)
- [PayOrderDivisionProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderDivisionProcessService.java#L35-L298)

### 密钥与配置安全

安全地管理商户密钥和配置是系统稳定运行的关键：
1. **密钥存储**：应用私钥（appSecret）应加密存储，避免明文暴露。系统通过`StringKit.str2Star`方法在查询时对密钥进行脱敏处理。
2. **访问控制**：通过`WebSecurityConfig`实现基于角色的访问控制（RBAC），确保只有授权用户才能访问敏感操作。
3. **配置管理**：支付接口配置参数（ifParams）以JSON字符串形式存储，支持动态更新，避免硬编码。
4. **签名验证**：使用`JeepayKit.getSign`方法生成和验证请求签名，防止数据篡改和重放攻击。
5. **安全审计**：记录关键操作的日志，包括创建、更新和删除操作，便于安全审计和追踪。

**Section sources**
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L27-L96)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java#L25-L260)
- [WebSecurityConfig.java](file://sys-merchant\src\main\java\com\unipay\mch\secruity\WebSecurityConfig.java)