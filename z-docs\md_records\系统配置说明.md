# UniPay 系统配置说明

## 概述

UniPay 系统的配置管理通过数据库中的 `t_sys_config` 表进行统一管理，主要包含系统应用配置（applicationConfig）分组下的各个平台网址配置。这些配置项用于生成各种链接、回调地址和资源访问路径。

## 系统应用配置项详解

### 1. mgrSiteUrl - 运营平台网址

**配置键**: `mgrSiteUrl`  
**默认值**: `http://127.0.0.1:9217`  
**用途**:
- 运营平台的访问地址
- 用于生成运营平台相关的链接和回调地址
- 系统内部跳转和重定向使用

**使用场景**:
- 邮件通知中的链接生成
- 系统间跳转
- API 回调地址生成

### 2. mchSiteUrl - 商户平台网址

**配置键**: `mchSiteUrl`  
**默认值**: `http://127.0.0.1:9218`  
**用途**:
- 商户平台的访问地址
- 商户相关功能的链接生成
- 商户登录后的跳转地址

**使用场景**:
- 商户注册成功后的跳转
- 商户相关邮件通知
- 商户API回调地址

### 3. agentSiteUrl - 代理商平台网址

**配置键**: `agentSiteUrl`  
**默认值**: `http://127.0.0.1:9219`  
**用途**:
- 代理商平台的访问地址
- 代理商相关功能的链接生成
- 代理商登录后的跳转地址

**使用场景**:
- 代理商注册成功后的跳转
- 代理商相关邮件通知
- 代理商API回调地址
- 代理商分润结算通知

### 4. paySiteUrl - 支付网关地址

**配置键**: `paySiteUrl`  
**默认值**: `http://127.0.0.1:9216`  
**用途**:
- 支付网关服务的访问地址
- 支付相关API的基础URL
- 收银台页面地址生成

**使用场景**:
- 生成统一收银台跳转地址 (`genUniJsapiPayUrl`)
- OAuth2回调地址生成 (`genOauth2RedirectUrlEncode`)
- 支付二维码图片地址生成 (`genScanImgUrl`)
- 支付宝ISV子商户授权链接 (`genAlipayIsvsubMchAuthUrl`)
- 微信转账用户确认链接 (`genTransferUserConfirm`)

### 5. ossPublicSiteUrl - 公共OSS访问地址

**配置键**: `ossPublicSiteUrl`  
**默认值**: `http://127.0.0.1:9217/api/anon/localOssFiles`  
**用途**:
- 文件上传后的公共访问地址
- 静态资源访问路径
- 图片、文档等文件的访问前缀

**使用场景**:
- 用户头像显示
- 商户资质文件访问
- 支付接口图标显示
- 系统Logo等静态资源

## 配置管理

### 数据库表结构

```sql
CREATE TABLE `t_sys_config` (
    `config_key` VARCHAR(50) NOT NULL COMMENT '配置KEY',
    `config_name` VARCHAR(50) NOT NULL COMMENT '配置名称',
    `config_desc` VARCHAR(200) NOT NULL COMMENT '描述信息',
    `group_key` VARCHAR(50) NOT NULL COMMENT '分组key',
    `group_name` VARCHAR(50) NOT NULL COMMENT '分组名称',
    `config_val` TEXT NOT NULL COMMENT '配置内容项',
    `type` VARCHAR(20) NOT NULL DEFAULT 'text' COMMENT '类型',
    `sort_num` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '显示顺序',
    `updated_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

### 配置访问方式

1. **通过管理后台**: 系统管理 -> 系统配置 -> 应用配置
2. **通过API**: `GET /api/sysConfigs/applicationConfig`
3. **代码中获取**: 通过 `SysConfigService.getDBApplicationConfig()` 方法

### 配置更新

配置更新后会通过MQ异步通知各个服务刷新缓存，确保配置的实时性。

## 部署注意事项

1. **生产环境配置**: 需要将默认的 `127.0.0.1` 地址替换为实际的域名或IP地址
2. **HTTPS支持**: 生产环境建议使用HTTPS协议
3. **负载均衡**: 如果使用负载均衡，需要配置为负载均衡器的地址
4. **端口配置**: 确保配置的端口与实际服务监听端口一致

## 配置示例

### 开发环境
```
mgrSiteUrl: http://127.0.0.1:9217
mchSiteUrl: http://127.0.0.1:9218
agentSiteUrl: http://127.0.0.1:9219
paySiteUrl: http://127.0.0.1:9216
ossPublicSiteUrl: http://127.0.0.1:9217/api/anon/localOssFiles
```

### 生产环境
```
mgrSiteUrl: https://admin.unipay.com
mchSiteUrl: https://merchant.unipay.com
agentSiteUrl: https://agent.unipay.com
paySiteUrl: https://pay.unipay.com
ossPublicSiteUrl: https://cdn.unipay.com
```
