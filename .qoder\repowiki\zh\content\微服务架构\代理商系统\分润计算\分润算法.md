# 分润算法

<cite>
**本文档引用文件**   
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java)
- [MchDivisionReceiverGroup.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [IDivisionService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IDivisionService.java)
- [WxpayDivisionService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayDivisionService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
</cite>

## 目录
1. [引言](#引言)
2. [分润计算核心逻辑](#分润计算核心逻辑)
3. [多级代理体系下的分润分配策略](#多级代理体系下的分润分配策略)
4. [金额精度处理机制](#金额精度处理机制)
5. [幂等性设计](#幂等性设计)
6. [关键代码实现分析](#关键代码实现分析)
7. [分润流程序列图](#分润流程序列图)
8. [总结](#总结)

## 引言
分润算法是支付系统中的核心功能之一，用于在交易完成后，根据预设的分润比例将收益分配给各级代理商。本系统通过灵活的配置机制支持多级代理分润，并确保计算的准确性和系统的稳定性。分润过程涉及交易金额计算、分润比例叠加、金额精度控制以及防止重复处理的幂等性设计。

## 分润计算核心逻辑
分润计算的核心在于根据交易金额和预设的分润比例，精确计算各级代理商应得的分润金额。系统首先确定可用于分润的基数，即商户实际入账金额，然后根据各接收方的分润比例进行分配。

分润基数计算公式为：
```
分润基数 = 支付金额 - 商户手续费 - 已退款金额 - 已成功分润金额
```

该逻辑在 `PayOrderService.calMchIncomeAmount()` 方法中实现，确保每次分润都基于最新的财务状态进行计算。

**分润计算核心逻辑**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L370-L380)

## 多级代理体系下的分润分配策略
系统支持多级代理体系下的分润分配，通过分组和自定义接收者两种模式实现灵活的分润策略。

### 自动分账组模式
当订单配置为使用系统自动分账接收者时，系统会查询商户配置的自动分账组，获取该组内所有可用的分账接收者账号。这些账号的分润比例将被叠加计算，形成总的分润方案。

### 自定义分账列表模式
商户可以指定特定的分账接收者及其分润比例，系统会根据提供的接收者ID或组ID进行匹配，并允许临时覆盖默认的分润比例。这种模式提供了更高的灵活性，适用于特殊场景下的分润需求。

分润接收者的查询和过滤逻辑在 `PayOrderDivisionProcessService.queryReceiver()` 方法中实现，支持基于商户号、应用ID、支付接口代码等条件的精确匹配。

**多级代理体系下的分润分配策略**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L200-L298)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java#L23-L159)
- [MchDivisionReceiverGroup.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java#L22-L87)

## 金额精度处理机制
为确保分润计算的准确性，系统采用高精度的BigDecimal类型进行金额计算，并通过特定的舍入模式避免精度丢失。

### 分润金额计算
分润金额的计算通过 `AmountUtil.calPercentageFee()` 方法实现，该方法接收金额（单位：分）、分润比例和舍入模式三个参数。计算过程如下：
1. 将金额转换为BigDecimal类型
2. 与分润比例相乘
3. 按照指定的舍入模式（如BigDecimal.ROUND_FLOOR）保留0位小数
4. 转换为Long类型返回

### 防止金额溢出
在计算最后一个接收者的分润金额时，系统会检查剩余待分账金额。如果按比例计算的金额超过剩余金额，将直接使用剩余金额作为该接收者的分润金额，从而避免总分润金额超出可用基数。

```mermaid
flowchart TD
A[开始] --> B[获取分润基数]
B --> C[遍历分润接收者]
C --> D[计算当前接收者分润金额]
D --> E{是否为最后一个接收者?}
E --> |否| F[从剩余金额中减去当前分润金额]
E --> |是| G[取剩余金额与计算金额的较小值]
F --> C
G --> H[结束]
```

**金额精度处理机制**
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java#L117-L133)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L150-L175)

## 幂等性设计
为防止同一笔交易被重复计算分润，系统通过状态机和消息队列机制实现了完整的幂等性保障。

### 状态机控制
支付订单实体（PayOrder）包含分润状态字段（divisionState），其可能值包括：
- UNHAPPEN (0): 未发生分润
- WAIT_TASK (1): 等待分账任务处理
- ING (2): 分账处理中
- FINISH (3): 分账任务已结束

在处理分润请求前，系统会检查订单的当前分润状态，只有处于WAIT_TASK或UNHAPPEN状态的订单才允许进行分润处理。处理开始后，状态立即更新为ING，防止其他进程同时处理同一订单。

### 消息队列幂等
分润任务通过消息队列（PayOrderDivisionMQ）异步触发，消息包含支付订单号作为唯一标识。即使同一消息被多次发送或消费，由于订单状态的控制，实际的分润逻辑只会执行一次。

### 重发机制
系统支持分润失败后的重发功能，通过isResend标志位标识重发请求。重发时，系统直接查询数据库中状态为"待分账"的记录进行处理，而不是重新计算分润方案，确保结果的一致性。

**幂等性设计**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)

## 关键代码实现分析
分润算法的核心实现位于 `PayOrderDivisionProcessService.processPayOrderDivision()` 方法中，该方法协调了分润计算的各个步骤。

### 主要处理流程
1. **订单状态验证**：检查支付订单是否存在且分润状态正确
2. **状态更新**：将订单状态更新为"分账处理中"，实现操作互斥
3. **接收者查询**：根据配置获取所有分润接收者
4. **分润计算**：逐个计算每个接收者的分润金额
5. **记录持久化**：将分润记录保存到数据库
6. **调用渠道接口**：委托具体支付渠道实现分润操作
7. **结果处理**：根据渠道响应更新分润记录状态
8. **订单状态更新**：标记分润任务完成

### 异常处理
方法通过try-catch块捕获所有异常，确保即使在调用渠道接口失败的情况下，也能正确更新分润记录状态为"失败"，并记录错误信息。这保证了系统的最终一致性。

**关键代码实现分析**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L23-L205)

## 分润流程序列图
```mermaid
sequenceDiagram
participant MQ as 消息队列
participant Receiver as PayOrderDivisionMQReceiver
participant Service as PayOrderDivisionProcessService
participant DB as 数据库
participant Channel as 支付渠道
MQ->>Receiver : 发送分润消息
Receiver->>Service : 调用processPayOrderDivision
Service->>DB : 查询支付订单
DB-->>Service : 返回订单信息
Service->>DB : 更新订单状态为ING
DB-->>Service : 更新成功
Service->>DB : 查询分润接收者
DB-->>Service : 返回接收者列表
Service->>Service : 计算分润金额
Service->>DB : 保存分润记录
DB-->>Service : 保存成功
Service->>Channel : 调用singleDivision
Channel-->>Service : 返回处理结果
Service->>DB : 更新分润记录状态
DB-->>Service : 更新成功
Service->>DB : 更新订单状态为FINISH
DB-->>Service : 更新成功
Service-->>Receiver : 返回结果
Receiver-->>MQ : 处理完成
```

**分润流程序列图**
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L15-L33)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)
- [IDivisionService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IDivisionService.java#L16-L33)
- [WxpayDivisionService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayDivisionService.java#L35-L381)

## 总结
本分润算法设计充分考虑了准确性、灵活性和系统稳定性。通过精确的金额计算、多级代理支持、严格的精度控制和完善的幂等性机制，确保了在各种业务场景下都能正确、可靠地完成收益分配。系统的模块化设计使得不同支付渠道的分润逻辑可以轻松集成，同时消息队列的使用保证了高并发场景下的处理性能。