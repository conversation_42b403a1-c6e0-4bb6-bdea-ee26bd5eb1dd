// 平台配置
const platformConfig = {
    manager: {
        name: '运营平台',
        port: '9217',
        path: '/'
    },
    agent: {
        name: '代理商平台', 
        port: '9219',
        path: '/'
    },
    merchant: {
        name: '商户平台',
        port: '9218', 
        path: '/'
    }
};

// 获取当前域名
function getCurrentDomain() {
    return window.location.hostname;
}

// 跳转到指定平台
function goToPlatform(platform) {
    const config = platformConfig[platform];
    if (!config) {
        console.error('未知的平台:', platform);
        return;
    }
    
    const domain = getCurrentDomain();
    const protocol = window.location.protocol;
    
    // 构建目标URL
    let targetUrl;
    
    // 如果是本地开发环境，直接使用端口
    if (domain === 'localhost' || domain === '127.0.0.1') {
        targetUrl = `${protocol}//${domain}:${config.port}${config.path}`;
    } else {
        // 生产环境，假设使用子域名或路径代理
        // 这里可以根据实际的nginx配置调整
        targetUrl = `${protocol}//${domain}:${config.port}${config.path}`;
    }
    
    // 显示加载提示
    showLoadingMessage(config.name);
    
    // 延迟跳转，给用户一个视觉反馈
    setTimeout(() => {
        window.open(targetUrl, '_blank');
        hideLoadingMessage();
    }, 500);
}

// 显示加载消息
function showLoadingMessage(platformName) {
    // 创建加载提示元素
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-message';
    loadingDiv.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>正在跳转到${platformName}...</p>
        </div>
    `;
    
    // 添加样式
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-size: 1.2rem;
    `;
    
    document.body.appendChild(loadingDiv);
    
    // 添加加载动画样式
    const style = document.createElement('style');
    style.textContent = `
        .loading-content {
            text-align: center;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
}

// 隐藏加载消息
function hideLoadingMessage() {
    const loadingDiv = document.getElementById('loading-message');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// 平滑滚动
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 导航链接点击事件
document.addEventListener('DOMContentLoaded', function() {
    // 为导航链接添加平滑滚动
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href');
            if (target.startsWith('#')) {
                smoothScroll(target);
            }
        });
    });
    
    // 为"立即开始"按钮添加滚动事件
    const startBtn = document.querySelector('a[href="#platforms"]');
    if (startBtn) {
        startBtn.addEventListener('click', function(e) {
            e.preventDefault();
            smoothScroll('#platforms');
        });
    }
    
    // 为"了解更多"按钮添加滚动事件
    const learnMoreBtn = document.querySelector('a[href="#about"]');
    if (learnMoreBtn) {
        learnMoreBtn.addEventListener('click', function(e) {
            e.preventDefault();
            smoothScroll('#about');
        });
    }
    
    // 添加滚动时导航栏效果
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    });
    
    // 添加平台卡片悬停效果
    const platformCards = document.querySelectorAll('.platform-card');
    platformCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 添加页面加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease-in-out';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Alt + 1: 运营平台
    if (e.altKey && e.key === '1') {
        e.preventDefault();
        goToPlatform('manager');
    }
    // Alt + 2: 代理商平台
    else if (e.altKey && e.key === '2') {
        e.preventDefault();
        goToPlatform('agent');
    }
    // Alt + 3: 商户平台
    else if (e.altKey && e.key === '3') {
        e.preventDefault();
        goToPlatform('merchant');
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面发生错误:', e.error);
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('页面已隐藏');
    } else {
        console.log('页面已显示');
    }
});

// 导出函数供全局使用
window.goToPlatform = goToPlatform;
window.smoothScroll = smoothScroll;
