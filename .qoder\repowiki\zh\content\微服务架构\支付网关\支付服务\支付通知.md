# 支付通知

<cite>
**本文档引用的文件**   
- [ChannelNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java)
- [IChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)
- [AbstractChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractChannelNoticeService.java)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [MchNotifyRecordService.java](file://service/src/main/java/com/unipay/service/impl/MchNotifyRecordService.java)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
</cite>

## 目录
1. [介绍](#介绍)
2. [支付通知机制概述](#支付通知机制概述)
3. [异步通知处理流程](#异步通知处理流程)
4. [通知签名验证](#通知签名验证)
5. [重复通知过滤](#重复通知过滤)
6. [通知内容解析](#通知内容解析)
7. [商户通知构建与推送](#商户通知构建与推送)
8. [通知重试机制](#通知重试机制)
9. [失败处理策略](#失败处理策略)
10. [商户确认机制](#商户确认机制)
11. [安全处理支付回调](#安全处理支付回调)
12. [代码示例](#代码示例)

## 介绍
支付通知机制是支付系统中的关键组成部分，负责处理来自各支付渠道的异步通知，并将支付结果安全可靠地通知到商户系统。本文档详细说明了支付通知机制的实现细节，包括通知接收、验证、处理和推送的完整流程。

## 支付通知机制概述
支付通知机制主要由`ChannelNoticeController`、`IChannelNoticeService`实现类和`PayMchNotifyService`三个核心组件构成。`ChannelNoticeController`负责接收来自支付渠道的异步通知，`IChannelNoticeService`实现类负责处理特定支付渠道的通知，而`PayMchNotifyService`负责构建商户通知内容并通过消息队列进行异步推送。

```mermaid
graph TB
A[支付渠道] --> B[ChannelNoticeController]
B --> C[IChannelNoticeService实现类]
C --> D[PayMchNotifyService]
D --> E[消息队列]
E --> F[PayOrderMchNotifyMQReceiver]
F --> G[商户系统]
```

**图表来源**
- [ChannelNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java)
- [IChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)

## 异步通知处理流程
`ChannelNoticeController`通过`doNotify`方法接收来自支付渠道的异步通知。该方法首先验证`ifCode`参数，然后根据`ifCode`获取对应的`IChannelNoticeService`实现类，解析通知参数，获取支付订单信息，最后调用`doNotice`方法处理通知。

```mermaid
sequenceDiagram
participant 支付渠道
participant ChannelNoticeController
participant IChannelNoticeService
participant PayOrderService
participant PayMchNotifyService
participant PayOrderProcessService
支付渠道->>ChannelNoticeController : 发送异步通知
ChannelNoticeController->>ChannelNoticeController : 验证ifCode
ChannelNoticeController->>ChannelNoticeController : 获取IChannelNoticeService实现类
ChannelNoticeController->>IChannelNoticeService : 调用parseParams解析参数
IChannelNoticeService-->>ChannelNoticeController : 返回订单号和参数
ChannelNoticeController->>PayOrderService : 查询支付订单
PayOrderService-->>ChannelNoticeController : 返回支付订单
ChannelNoticeController->>ConfigContextQueryService : 查询商户应用配置
ConfigContextQueryService-->>ChannelNoticeController : 返回配置信息
ChannelNoticeController->>IChannelNoticeService : 调用doNotice处理通知
IChannelNoticeService-->>ChannelNoticeController : 返回处理结果
ChannelNoticeController->>PayOrderService : 更新订单状态
PayOrderService-->>ChannelNoticeController : 返回更新结果
alt 订单支付成功
ChannelNoticeController->>PayOrderProcessService : 调用confirmSuccess
PayOrderProcessService->>PayMchNotifyService : 调用payOrderNotify
PayMchNotifyService-->>ChannelNoticeController : 通知推送完成
end
ChannelNoticeController-->>支付渠道 : 返回响应
```

**图表来源**
- [ChannelNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java)
- [IChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)

## 通知签名验证
通知签名验证是确保通知来源合法性的关键步骤。不同支付渠道采用不同的签名验证方式：

- **支付宝**：使用`AlipaySignature.rsaCheckV1`或`AlipaySignature.rsaCertCheckV1`方法验证签名
- **微信**：V2版本使用`result.checkResult`方法验证，V3版本通过`wxPayService.parseOrderNotifyV3Result`自动验证
- **云闪付**：使用`YsfSignUtils.validate`方法验证签名
- **计全付**：使用`JeepayKit.getSign`方法生成签名并与通知中的签名对比
- **小新支付**：使用`XxpayKit.getSign`方法生成签名并与通知中的签名对比

```mermaid
flowchart TD
A[接收通知] --> B{支付渠道}
B --> |支付宝| C[使用AlipaySignature验证]
B --> |微信V2| D[使用checkResult验证]
B --> |微信V3| E[使用parseOrderNotifyV3Result验证]
B --> |云闪付| F[使用YsfSignUtils.validate验证]
B --> |计全付| G[使用JeepayKit.getSign对比]
B --> |小新支付| H[使用XxpayKit.getSign对比]
C --> I{验证成功?}
D --> I
E --> I
F --> I
G --> I
H --> I
I --> |是| J[继续处理]
I --> |否| K[返回错误响应]
```

**图表来源**
- [AlipayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayChannelNoticeService.java)
- [WxpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayChannelNoticeService.java)
- [YsfpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ysfpay/YsfpayChannelNoticeService.java)
- [PlspayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/plspay/PlspayChannelNoticeService.java)
- [XxpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/xxpay/XxpayChannelNoticeService.java)
- [JeepayKit.java](file://core/src/main/java/com/unipay/core/utils/JeepayKit.java)

## 重复通知过滤
系统通过`MchNotifyRecord`实体来防止重复通知。当`PayMchNotifyService`准备推送通知时，会先查询是否存在对应的`MchNotifyRecord`记录。如果已存在，则不再发送新的通知，从而避免了重复通知的问题。

```mermaid
flowchart TD
A[支付订单状态变更] --> B[调用payOrderNotify]
B --> C[检查notifyUrl是否为空]
C --> |为空| D[结束]
C --> |不为空| E[查询MchNotifyRecord]
E --> F{记录已存在?}
F --> |是| G[结束]
F --> |否| H[创建MchNotifyRecord]
H --> I[保存到数据库]
I --> J{保存成功?}
J --> |否| K[结束]
J --> |是| L[发送MQ消息]
L --> M[结束]
```

**图表来源**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [MchNotifyRecordService.java](file://service/src/main/java/com/unipay/service/impl/MchNotifyRecordService.java)

## 通知内容解析
通知内容解析由`IChannelNoticeService`接口的`parseParams`方法完成。不同支付渠道的通知格式不同，因此各自的实现类需要根据渠道特点解析通知内容：

- **支付宝**：从JSON参数中提取`out_trade_no`作为订单号
- **微信V2**：从XML中解析`WxPayOrderNotifyResult`，提取`outTradeNo`作为订单号
- **微信V3**：从加密数据中解析`WxPayNotifyV3Result`，提取`outTradeNo`作为订单号
- **云闪付**：从JSON参数中提取`orderNo`作为订单号
- **计全付**：从JSON参数中提取`mchOrderNo`作为订单号
- **小新支付**：从JSON参数中提取`mchOrderNo`作为订单号

```mermaid
classDiagram
class IChannelNoticeService {
<<interface>>
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
+doNotifyOrderStateUpdateFail(request) ResponseEntity
+doNotifyOrderNotExists(request) ResponseEntity
}
class AbstractChannelNoticeService {
<<abstract>>
-requestKitBean RequestKitBean
-channelCertConfigKitBean ChannelCertConfigKitBean
-configContextQueryService ConfigContextQueryService
+doNotifyOrderNotExists(request) ResponseEntity
+doNotifyOrderStateUpdateFail(request) ResponseEntity
+textResp(text) ResponseEntity
+jsonResp(body) ResponseEntity
+getReqParamJSON() JSONObject
+getReqParamFromBody() String
+getCertFilePath(certFilePath) String
+getCertFile(certFilePath) File
}
class AlipayChannelNoticeService {
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
}
class WxpayChannelNoticeService {
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
}
class YsfpayChannelNoticeService {
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
}
class PlspayChannelNoticeService {
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
}
class XxpayChannelNoticeService {
+getIfCode() String
+parseParams(request, urlOrderId, noticeTypeEnum) MutablePair~String, Object~
+doNotice(request, params, payOrder, mchAppConfigContext, noticeTypeEnum) ChannelRetMsg
}
IChannelNoticeService <|-- AbstractChannelNoticeService
AbstractChannelNoticeService <|-- AlipayChannelNoticeService
AbstractChannelNoticeService <|-- WxpayChannelNoticeService
AbstractChannelNoticeService <|-- YsfpayChannelNoticeService
AbstractChannelNoticeService <|-- PlspayChannelNoticeService
AbstractChannelNoticeService <|-- XxpayChannelNoticeService
```

**图表来源**
- [IChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java)
- [AbstractChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/AbstractChannelNoticeService.java)
- [AlipayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayChannelNoticeService.java)
- [WxpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayChannelNoticeService.java)
- [YsfpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ysfpay/YsfpayChannelNoticeService.java)
- [PlspayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/plspay/PlspayChannelNoticeService.java)
- [XxpayChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/xxpay/XxpayChannelNoticeService.java)

## 商户通知构建与推送
`PayMchNotifyService`负责构建商户通知内容并通过消息队列进行异步推送。通知内容包括支付订单的详细信息，并使用商户的`appSecret`进行签名，确保通知的完整性和安全性。

```mermaid
sequenceDiagram
participant PayOrderProcessService
participant PayMchNotifyService
participant MchNotifyRecordService
participant IMQSender
participant PayOrderMchNotifyMQ
PayOrderProcessService->>PayMchNotifyService : 调用payOrderNotify
PayMchNotifyService->>PayOrderService : 检查notifyUrl
PayOrderService-->>PayMchNotifyService : 返回结果
alt notifyUrl为空
PayMchNotifyService-->>PayOrderProcessService : 结束
end
PayMchNotifyService->>MchNotifyRecordService : 查询MchNotifyRecord
MchNotifyRecordService-->>PayMchNotifyService : 返回结果
alt 记录已存在
PayMchNotifyService-->>PayOrderProcessService : 结束
end
PayMchNotifyService->>ConfigContextQueryService : 查询appSecret
ConfigContextQueryService-->>PayMchNotifyService : 返回appSecret
PayMchNotifyService->>PayMchNotifyService : 构建notifyUrl
PayMchNotifyService->>MchNotifyRecordService : 保存MchNotifyRecord
MchNotifyRecordService-->>PayMchNotifyService : 返回结果
alt 保存失败
PayMchNotifyService-->>PayOrderProcessService : 结束
end
PayMchNotifyService->>IMQSender : 发送PayOrderMchNotifyMQ
IMQSender->>PayOrderMchNotifyMQ : 调用build
PayOrderMchNotifyMQ-->>IMQSender : 返回MQ实例
IMQSender-->>PayMchNotifyService : 发送完成
PayMchNotifyService-->>PayOrderProcessService : 结束
```

**图表来源**
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [MchNotifyRecordService.java](file://service/src/main/java/com/unipay/service/impl/MchNotifyRecordService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)

## 通知重试机制
系统通过消息队列实现了通知重试机制。`PayOrderMchNotifyMQReceiver`接收到MQ消息后，会尝试向商户系统发送通知。如果发送失败，会根据当前通知次数决定是否继续重试。

```mermaid
flowchart TD
A[接收MQ消息] --> B[查询MchNotifyRecord]
B --> C{记录存在且状态为通知中?}
C --> |否| D[结束]
C --> |是| E{已达到最大发送次数?}
E --> |是| F[结束]
E --> |否| G[发送HTTP请求]
G --> H{发送成功?}
H --> |是| I[更新状态为成功]
H --> |否| J{已达到最大发送次数?}
J --> |是| K[更新状态为失败]
J --> |否| L[更新状态为通知中]
L --> M[发送延迟MQ消息]
M --> N[结束]
I --> N
K --> N
```

**图表来源**
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)

## 失败处理策略
系统采用分级的失败处理策略。当通知发送失败时，系统会根据失败次数决定后续操作：

1. 首次通知成功：更新支付订单的`notifyState`为已通知
2. 通知失败但未达到最大次数：更新通知记录状态为"通知中"，并发送延迟MQ消息进行重试
3. 达到最大通知次数仍失败：更新通知记录状态为"失败"

这种策略确保了通知的可靠性，同时避免了无限重试导致的系统资源浪费。

## 商户确认机制
商户确认机制通过HTTP响应码和响应内容实现。支付渠道要求商户系统在接收到通知后返回特定的响应，以确认已成功接收通知：

- **支付宝**：返回`"success"`文本
- **微信**：返回`"<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>"`或JSON `{"code": "SUCCESS", "message": "成功"}`
- **云闪付**：返回`"success"`文本
- **计全付**：返回`"success"`文本
- **小新支付**：返回`"success"`文本

```mermaid
sequenceDiagram
participant 支付渠道
participant ChannelNoticeController
participant IChannelNoticeService
participant 商户系统
支付渠道->>ChannelNoticeController : 发送通知
ChannelNoticeController->>IChannelNoticeService : 处理通知
IChannelNoticeService-->>ChannelNoticeController : 返回响应
ChannelNoticeController-->>支付渠道 : 返回确认响应
支付渠道->>商户系统 : 发送通知
商户系统-->>支付渠道 : 返回确认响应
```

**图表来源**
- [IChannelNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelNoticeService.java)
- [ChannelNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java)

## 安全处理支付回调
安全处理支付回调是支付系统的关键。系统通过以下措施确保回调处理的安全性：

1. **签名验证**：验证通知来源的合法性
2. **金额核对**：核对通知中的金额与系统记录的金额是否一致
3. **订单状态检查**：确保订单处于可更新状态
4. **幂等性处理**：通过`MchNotifyRecord`防止重复通知
5. **异常处理**：捕获并处理各种异常情况

## 代码示例
以下是安全处理支付回调的关键代码示例：

```mermaid
flowchart TD
A[开始] --> B[验证ifCode]
B --> C[获取IChannelNoticeService]
C --> D[解析通知参数]
D --> E[查询支付订单]
E --> F[查询商户配置]
F --> G[处理通知]
G --> H[更新订单状态]
H --> I[执行后续业务逻辑]
I --> J[返回响应]
J --> K[结束]
style A fill:#f9f,stroke:#333,stroke-width:2px
style K fill:#f9f,stroke:#333,stroke-width:2px
```

**图表来源**
- [ChannelNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/ChannelNoticeController.java)