# 分润计算

<cite>
**本文档引用文件**  
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)
- [AgentProfitRecordMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentProfitRecordMapper.java)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml)
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java)
</cite>

## 目录
1. [分润算法实现逻辑](#分润算法实现逻辑)
2. [AgentProfitRecord实体设计](#agentprofitrecord实体设计)
3. [分润计算触发时机与异步处理](#分润计算触发时机与异步处理)
4. [核心代码分析](#核心代码分析)
5. [分润记录查询接口实现](#分润记录查询接口实现)

## 分润算法实现逻辑

分润计算的核心逻辑基于支付订单的商户手续费，通过预设的分润比例为代理商分配收益。系统支持多级代理的分润分配策略，通过分账接收者配置实现灵活的分润规则。

分润金额的计算公式为：**分润金额 = 商户手续费 × 分润比例**。该计算在 `AgentProfitRecordService.createProfitRecord` 方法中实现，其中手续费和分润比例均以“分”为单位进行计算，确保精度。

在分账处理过程中，系统首先验证支付订单状态是否为“等待分账任务处理”或“未发生分账”，确保分账操作的合法性。随后，系统根据订单信息查询所有可用的分账接收者，这些接收者可以是系统自动配置的分账组成员，也可以是商户自定义的接收者列表。对于自定义列表，系统允许在调用时覆盖默认的分润比例。

为避免金额溢出，系统采用向下取整策略计算分账金额。在 `PayOrderDivisionProcessService` 中，首先计算所有接收者的分润比例总和，然后使用 `AmountUtil.calPercentageFee` 方法计算总分账金额。在为每个接收者分配金额时，系统会动态调整剩余待分账金额，确保最后一个接收者的分账金额不会超出剩余总额。

```mermaid
flowchart TD
Start([开始分润计算]) --> ValidateOrder["验证订单状态"]
ValidateOrder --> OrderValid{"订单状态有效?"}
OrderValid --> |否| ThrowError["抛出异常"]
OrderValid --> |是| QueryReceiver["查询分账接收者"]
QueryReceiver --> CalcTotal["计算总分润比例"]
CalcTotal --> CalcAmount["计算总分账金额"]
CalcAmount --> LoopStart["遍历每个接收者"]
LoopStart --> CalcIndividual["计算单个接收者分账金额"]
CalcIndividual --> AdjustRemain["调整剩余金额"]
AdjustRemain --> SaveRecord["保存分账记录"]
SaveRecord --> CheckLast{"是否为最后一个?"}
CheckLast --> |否| LoopStart
CheckLast --> |是| CallChannel["调用渠道分账接口"]
CallChannel --> UpdateOrder["更新订单分账状态"]
UpdateOrder --> End([结束])
```

**图示来源**  
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java#L115-L136)

**本节来源**  
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L43-L83)

## AgentProfitRecord实体设计

`AgentProfitRecord` 实体用于记录代理商的分润明细，是分润计算功能的核心数据结构。该实体设计遵循高精度、可追溯和状态可管理的原则。

实体的主要字段包括：
- **ID**: 主键，自增
- **代理商号 (agentNo)**: 标识分润归属的代理商
- **商户号 (mchNo)**: 产生分润的商户
- **支付订单号 (payOrderId)**: 关联的支付订单
- **订单金额 (orderAmount)**: 订单总金额，单位为分
- **商户手续费 (mchFeeAmount)**: 商户支付的手续费，单位为分
- **分润比例 (profitRate)**: 代理商的分润比例，使用 `BigDecimal` 类型确保精度
- **分润金额 (profitAmount)**: 计算得出的分润金额，单位为分
- **分润日期 (profitDate)**: 分润计算的日期
- **分润状态 (state)**: 分润的当前状态（0-待结算, 1-已结算, 2-已取消）
- **结算时间 (settleTime)**: 分润结算的时间戳
- **备注 (remark)**: 附加说明信息

该实体通过 `@TableName("t_agent_profit_record")` 注解映射到数据库表 `t_agent_profit_record`。所有金额字段均以“分”为单位存储，避免了浮点数精度问题。分润状态使用枚举值定义，确保状态转换的可控性。

```mermaid
erDiagram
t_agent_profit_record {
bigint id PK
varchar agent_no
varchar mch_no
varchar pay_order_id
bigint order_amount
bigint mch_fee_amount
decimal profit_rate
bigint profit_amount
date profit_date
tinyint state
datetime settle_time
varchar remark
datetime created_at
datetime updated_at
}
```

**图示来源**  
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)

**本节来源**  
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)

## 分润计算触发时机与异步处理

分润计算的触发时机主要在支付成功后，由系统自动或商户手动触发。系统通过消息队列（MQ）实现异步处理机制，确保主支付流程的高效性。

当支付订单状态变为“支付成功”时，系统会根据订单的分账模式决定是否触发分润计算。对于自动分账模式（`divisionMode = 1`），系统会立即向 `QUEUE_PAY_ORDER_DIVISION` 队列发送一个 `PayOrderDivisionMQ` 消息。该消息包含支付订单号、是否使用系统默认分组、分账接收者列表和是否重发等关键信息。

`PayOrderDivisionMQReceiver` 作为消息消费者，监听该队列。当接收到消息时，它会调用 `PayOrderDivisionProcessService.processPayOrderDivision` 方法执行分润计算。这种异步处理机制将分润计算与支付流程解耦，避免了因分账接口调用延迟而影响支付体验。

异步处理流程如下：
1. 支付成功后，生成 `PayOrderDivisionMQ` 消息并发送到队列
2. `PayOrderDivisionMQReceiver` 接收消息并调用分润处理服务
3. 分润服务查询订单信息和分账接收者配置
4. 计算分账金额并生成分账记录
5. 调用渠道侧分账接口执行分账
6. 更新分账记录和订单状态

```mermaid
sequenceDiagram
participant PaymentSystem as 支付系统
participant MQ as 消息队列
participant MQReceiver as MQ接收器
participant DivisionService as 分账服务
participant Channel as 支付渠道
PaymentSystem->>MQ : 发送PayOrderDivisionMQ消息
MQ->>MQReceiver : 传递消息
MQReceiver->>DivisionService : 调用processPayOrderDivision
DivisionService->>DivisionService : 验证订单状态
DivisionService->>DivisionService : 查询分账接收者
DivisionService->>DivisionService : 计算分账金额
DivisionService->>Channel : 调用渠道分账接口
Channel-->>DivisionService : 返回分账结果
DivisionService->>DivisionService : 更新分账记录
DivisionService->>PaymentSystem : 更新订单分账状态
```

**图示来源**  
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L15-L33)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)

**本节来源**  
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L15-L33)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L56-L58)

## 核心代码分析

分润计算的核心代码主要集中在 `PayOrderDivisionProcessService` 和 `AgentProfitRecordService` 两个服务类中，实现了金额精度处理和幂等性设计。

在金额精度处理方面，系统统一使用“分”作为金额单位，避免了浮点数计算的精度丢失问题。`AmountUtil.calPercentageFee` 工具方法使用 `BigDecimal` 进行百分比计算，并支持多种舍入模式（如四舍五入、向下取整）。在分账金额分配时，系统采用向下取整策略，确保总分账金额不超过可用金额。

幂等性设计是防止重复计算的关键。系统通过订单的分账状态实现幂等控制。在 `processPayOrderDivision` 方法中，首先检查订单状态是否为“等待分账任务处理”或“未发生分账”。如果状态不匹配，则抛出异常，阻止重复处理。在更新订单状态时，使用 MyBatis-Plus 的 `LambdaUpdateWrapper` 添加状态条件，确保只有在状态未改变的情况下才更新，避免并发问题。

```mermaid
classDiagram
class PayOrderDivisionProcessService {
+processPayOrderDivision(payOrderId, useSysAutoDivisionReceivers, receiverList, isResend) ChannelRetMsg
+genRecord(batchOrderId, payOrder, receiver, payOrderDivisionAmount, subDivisionAmount) PayOrderDivisionRecord
+queryReceiver(useSysAutoDivisionReceivers, payOrder, customerDivisionReceiverList) MchDivisionReceiver[]
}
class AgentProfitRecordService {
+createProfitRecord(agentNo, mchNo, payOrderId, orderAmount, mchFeeAmount, profitRate) boolean
+batchCreateProfitRecords(records) boolean
+batchSettleProfitRecords(ids) boolean
+batchCancelProfitRecords(ids) boolean
+existsProfitRecord(agentNo, payOrderId) boolean
}
class AmountUtil {
+calPercentageFee(amount, rate) Long
+calPercentageFee(amount, rate, mode) Long
}
PayOrderDivisionProcessService --> AmountUtil : "使用"
AgentProfitRecordService --> AmountUtil : "使用"
PayOrderDivisionProcessService --> PayOrderDivisionRecord : "生成"
AgentProfitRecordService --> AgentProfitRecord : "创建"
```

**图示来源**  
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L24-L167)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java#L115-L136)

**本节来源**  
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L43-L83)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java#L115-L136)

## 分润记录查询接口实现

分润记录查询接口由 `ProfitRecordController` 提供，支持按时间范围、代理商级别等条件筛选。接口实现遵循 RESTful 规范，返回标准化的 API 响应。

查询接口主要包括：
- **分润记录列表**: 支持分页查询，可按商户号、分润状态、开始日期和结束日期筛选
- **分润记录详情**: 根据记录ID查询单条分润记录的详细信息
- **分润统计**: 统计指定条件下的分润总额、订单总额等聚合数据
- **今日/本月分润统计**: 快速获取当前代理商的分润统计信息

在 `selectProfitRecordPage` 方法中，系统首先获取当前登录用户的代理商号，确保数据隔离。然后调用 `AgentProfitRecordService` 的分页查询方法，该方法最终执行 `AgentProfitRecordMapper.xml` 中定义的 SQL 查询。SQL 使用动态条件构建，仅当参数不为空时才添加相应的 WHERE 子句。

权限控制通过 Spring Security 的 `@PreAuthorize` 注解实现。例如，查看分润记录需要 `ENT_PROFIT_RECORD_LIST` 权限，查看详情需要 `ENT_PROFIT_RECORD_VIEW` 权限。在查询详情时，系统还会验证当前用户是否有权查看该记录，防止越权访问。

```mermaid
flowchart TD
Start([HTTP请求]) --> AuthCheck["身份验证"]
AuthCheck --> PermissionCheck["权限检查"]
PermissionCheck --> ParamValidate["参数验证"]
ParamValidate --> DataQuery["数据查询"]
DataQuery --> FilterByAgent["按代理商号过滤"]
FilterByAgent --> ApplyConditions["应用筛选条件"]
ApplyConditions --> ExecuteSQL["执行SQL查询"]
ExecuteSQL --> FormatResult["格式化结果"]
FormatResult --> ReturnResponse["返回API响应"]
```

**图示来源**  
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java#L59-L93)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L28-L49)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L24-L41)

**本节来源**  
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java#L59-L93)
- [AgentProfitRecordMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentProfitRecordMapper.java#L21-L71)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L28-L49)