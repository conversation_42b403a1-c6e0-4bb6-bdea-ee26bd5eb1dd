
# 关闭订单API

<cite>
**本文档引用的文件**  
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java)
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java)
- [ClosePayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRS.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [IPayOrderCloseService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderCloseService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)
</cite>

## 目录
1. [简介](#简介)
2. [接口定义](#接口定义)
3. [请求参数](#请求参数)
4. [响应参数](#响应参数)
5. [状态机约束](#状态机约束)
6. [业务流程](#业务流程)
7. [幂等性与重复请求处理](#幂等性与重复请求处理)
8. [错误码说明](#错误码说明)
9. [关联资源影响](#关联资源影响)
10. [前端交互最佳实践](#前端交互最佳实践)

## 简介

关闭订单API用于取消尚未完成支付的订单，适用于用户主动取消支付、超时未支付等场景。该接口通过商户号、应用ID和订单标识定位订单，并根据订单当前状态执行关闭操作。对于处于"订单生成"状态的订单，系统直接更新状态；对于处于"支付中"状态的订单，系统会调用上游支付渠道的关闭接口，成功后更新本地订单状态。

**Section sources**
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java#L39-L105)

## 接口定义

- **接口路径**: `POST /api/pay/close`
- **请求方式**: POST
- **内容类型**: `application/json`
- **认证方式**: 商户签名验证（基于`appId`和`appSecret`）

该接口继承自`ApiController`，具备完整的商户身份验证和签名校验机制。

**Section sources**
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java#L39-L105)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

## 请求参数

请求体为JSON格式，包含以下字段：

| 参数名 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `mchNo` | String | 是 | 商户号 |
| `appId` | String | 是 | 应用ID |
| `mchOrderNo` | String | 否 | 商户订单号（与`payOrderId`二选一） |
| `payOrderId` | String | 否 | 支付系统订单号（与`mchOrderNo`二选一） |
| `sign` | String | 是 | 签名值 |

**验证规则**:
- `mchNo` 和 `appId` 不能为空。
- `mchOrderNo` 和 `payOrderId` 不能同时为空。
- 请求必须通过签名验证，使用`appSecret`对除`sign`外的所有参数进行签名。

**Section sources**
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java#L12-L21)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java#L40-L86)

## 响应参数

响应体为JSON格式，遵循统一的`ApiRes`结构：

| 参数名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `code` | Integer | 业务响应码 |
| `msg` | String | 业务响应信息 |
| `data` | Object | 数据对象（`ClosePayOrderRS`） |
| `sign` | String | 签名值 |

`