package com.unipay.mgr.secruity;

import com.unipay.mgr.config.SystemYmlConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import static org.springframework.security.config.Customizer.withDefaults;

/**
 * 更安全的 Spring Security 配置
 * 使用精确路径匹配，避免过于宽泛的通配符
 *
 * <AUTHOR>
 * @date 2025/9/16
 */
//@Configuration  // 注释掉，作为参考配置
//@EnableWebSecurity
public class WebSecurityConfigSecure {

    @Autowired private UserDetailsService userDetailsService;
    @Autowired private JeeAuthenticationEntryPoint unauthorizedHandler;
    @Autowired private SystemYmlConfig systemYmlConfig;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(withDefaults())
                .addFilter(corsFilter())
                .headers(httpSecurityHeadersConfigurer -> httpSecurityHeadersConfigurer.cacheControl(HeadersConfigurer.CacheControlConfig::disable))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .exceptionHandling(exceptions -> exceptions.authenticationEntryPoint(unauthorizedHandler))
                .authenticationProvider(authenticationProvider())
                .addFilterBefore(new JeeAuthenticationTokenFilter(), UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests((auth) -> {
                    auth
                            // 静态资源允许匿名访问
                            .requestMatchers(HttpMethod.GET,
                                    "/", "/index.html", "/favicon.ico",
                                    "/assets/**", "/static/**", "/public/**",
                                    "/*.css", "/*.js", "/*.png", "/*.jpg", "/*.jpeg", 
                                    "/*.svg", "/*.ico", "/*.webp", "/*.woff", "/*.woff2", 
                                    "/*.ttf", "/*.eot", "/*.txt", "/*.mp4"
                            ).permitAll()
                            
                            // API和文档相关路径允许匿名访问
                            .requestMatchers(
                                    "/api/anon/**",
                                    "/webjars/**", "/v3/api-docs/**", "/doc.html", 
                                    "/knife4j/**", "/swagger-ui/**", "/swagger-resources/**"
                            ).permitAll()
                            
                            // 精确的前端路由路径（更安全的方式）
                            .requestMatchers(HttpMethod.GET,
                                    // 核心页面
                                    "/login", "/main", "/dashboard", "/welcome",
                                    // 用户管理
                                    "/users", "/roles", "/current",
                                    // 商户管理
                                    "/mch", "/mchApp", "/agent", 
                                    // 订单管理
                                    "/payOrder", "/refundOrder", "/transferOrder", "/mchNotify",
                                    // 支付配置
                                    "/payways", "/payconfig", "/apps", "/isv",
                                    "/pay", "/transfer", "/refund", "/notify", "/ifdefines", "/config",
                                    // 系统配置
                                    "/profitRecord", "/division", "/sysconfig", "/log"
                            ).permitAll()
                            
                            // API 接口需要认证
                            .requestMatchers("/api/**").authenticated()
                            
                            // 其他所有请求都需要认证（更严格）
                            .anyRequest().authenticated();
                });

        return http.build();
    }

    // 其他 Bean 配置保持不变...
    @Bean
    public UserDetailsService userDetailsService() {
        return username -> userDetailsService.loadUserByUsername(username);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService());
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    @Bean
    public AuthenticationManager authenticationManager() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService());
        authProvider.setPasswordEncoder(passwordEncoder());
        return new ProviderManager(authProvider);
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        if(systemYmlConfig.getAllowCors()){
            CorsConfiguration config = new CorsConfiguration();
            config.setAllowCredentials(true);
            config.addAllowedOriginPattern(CorsConfiguration.ALL);
            config.addAllowedHeader(CorsConfiguration.ALL);
            config.addAllowedMethod(CorsConfiguration.ALL);
            source.registerCorsConfiguration("/**", config);
        }
        return new CorsFilter(source);
    }
}