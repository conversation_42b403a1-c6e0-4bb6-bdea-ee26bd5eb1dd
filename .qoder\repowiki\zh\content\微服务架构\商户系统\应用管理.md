# 应用管理

<cite>
**Referenced Files in This Document**   
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java)
- [MchAppController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchAppController.java)
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java)
- [StringKit.java](file://core\src\main\java\com\unipay\core\utils\StringKit.java)
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java)
</cite>

## 目录
1. [应用管理子系统概述](#应用管理子系统概述)
2. [MchApp实体类设计](#mchapp实体类设计)
3. [应用密钥安全处理](#应用密钥安全处理)
4. [MchAppController API接口](#mchappcontroller-api接口)
5. [应用与支付通道配置关联](#应用与支付通道配置关联)
6. [应用创建与配置流程示例](#应用创建与配置流程示例)
7. [应用管理模块核心作用](#应用管理模块核心作用)

## 应用管理子系统概述

应用管理子系统是商户系统中的核心组件，负责管理商户应用的全生命周期。该子系统通过MchApp实体类定义应用的基本信息，包括应用ID、应用名称、商户号、应用状态和应用私钥等关键字段。系统提供了完整的API接口，支持创建应用、获取应用密钥、配置回调地址等操作。应用密钥作为敏感信息，系统采用了严格的安全处理机制，确保密钥在存储和传输过程中的安全性。此外，应用与支付通道配置之间存在紧密的关联关系，通过API接口可以方便地进行管理和配置。

**Section sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)

## MchApp实体类设计

MchApp实体类是应用管理的核心数据模型，定义了商户应用的各项属性。该类通过`@TableName("t_mch_app")`注解映射到数据库表t_mch_app，使用Lombok的`@Data`注解自动生成getter、setter、toString等方法，提高了代码的简洁性。

实体类包含以下关键字段：
- **appId**: 应用ID，作为主键，使用`@TableId`注解标识，类型为INPUT，由系统生成
- **appName**: 应用名称，用于标识应用的可读名称
- **mchNo**: 商户号，关联到具体的商户
- **state**: 应用状态，0表示停用，1表示正常
- **appSecret**: 应用私钥，用于API调用的身份验证
- **remark**: 备注信息，用于记录应用的额外说明
- **createdUid**: 创建者用户ID
- **createdBy**: 创建者姓名
- **createdAt**: 创建时间
- **updatedAt**: 更新时间

该实体类还提供了静态方法`gw()`，用于创建LambdaQueryWrapper，方便进行数据库查询操作。

**Section sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)

## 应用密钥安全处理

应用密钥（appSecret）作为系统安全的关键要素，其处理需要格外谨慎。系统采用了多层次的安全措施来保护应用密钥：

1. **存储安全**: 应用密钥在数据库中以明文存储，但通过访问控制和权限管理限制对敏感数据的访问。

2. **传输安全**: 在API响应中，系统会对应用密钥进行脱敏处理。通过`StringKit.str2Star()`方法，将密钥的中间部分替换为星号，只显示前6位和后6位，有效防止密钥在传输过程中被泄露。

3. **查询脱敏**: `MchAppService`的`selectById`和`selectPage`方法在返回数据前，都会调用`str2Star`方法对appSecret字段进行脱敏处理，确保敏感信息不会被完整暴露。

4. **权限控制**: 只有具有相应权限的用户才能访问应用密钥信息。系统通过`@PreAuthorize`注解实现细粒度的权限控制，确保只有授权用户才能执行相关操作。

```mermaid
flowchart TD
A[应用密钥生成] --> B[数据库存储]
B --> C[API查询请求]
C --> D{权限验证}
D --> |通过| E[获取密钥数据]
D --> |拒绝| F[返回权限错误]
E --> G[执行str2Star脱敏]
G --> H[返回脱敏后的密钥]
```

**Diagram sources**
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L58-L66)
- [StringKit.java](file://core\src\main\java\com\unipay\core\utils\StringKit.java#L116-L132)

**Section sources**
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L58-L66)
- [StringKit.java](file://core\src\main\java\com\unipay\core\utils\StringKit.java#L116-L132)

## MchAppController API接口

MchAppController提供了完整的RESTful API接口，用于管理商户应用。这些接口分布在不同的系统模块中，包括系统管理端、商户端和代理商端，满足不同角色的管理需求。

### 系统管理端API

系统管理端的MchAppController提供了全面的应用管理功能：

- **查询应用列表**: `GET /api/mchApps`，支持按商户号、应用ID、应用名称和状态进行筛选
- **新建应用**: `POST /api/mchApps`，创建新的商户应用，需要提供应用名称、应用私钥、商户号等信息
- **应用详情**: `GET /api/mchApps/{appId}`，获取指定应用的详细信息
- **更新应用信息**: `PUT /api/mchApps/{appId}`，修改应用的配置信息
- **删除应用**: `DELETE /api/mchApps/{appId}`，删除指定的应用

### 商户端API

商户端的API接口在权限控制上更加严格，确保商户只能管理自己的应用：

- **查询应用列表**: 仅返回当前商户的应用
- **新建应用**: 自动关联当前商户号，无需手动指定
- **应用详情**: 验证应用是否属于当前商户
- **更新和删除**: 均需验证应用与当前商户的归属关系

### 代理商端API

代理商端的API接口具有特殊的权限逻辑，允许代理商管理其下属商户的应用：

- **查询下属商户应用列表**: 根据代理商与商户的关联关系，返回其管理的所有商户的应用
- **为下属商户新建应用**: 需要指定商户号，并验证代理商是否有权限为该商户创建应用
- **查看、更新和删除**: 均需验证应用所属商户是否在代理商的管理范围内

所有API接口都通过`@PreAuthorize`注解进行权限控制，并在操作完成后通过消息队列发送配置更新通知，确保系统配置的实时同步。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Controller as MchAppController
participant Service as MchAppService
participant MQ as 消息队列
Client->>Controller : 发送API请求
Controller->>Controller : 权限验证(@PreAuthorize)
Controller->>Service : 调用业务逻辑
Service->>Service : 执行数据库操作
Service-->>Controller : 返回结果
Controller->>MQ : 发送配置更新消息(ResetIsvMchAppInfoConfigMQ)
Controller-->>Client : 返回响应
```

**Diagram sources**
- [MchAppController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchAppController.java#L30-L169)
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java#L35-L266)
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java#L16-L83)

**Section sources**
- [MchAppController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchAppController.java#L30-L169)
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [MchAppController.java](file://sys-agent\src\main\java\com\unipay\agent\ctrl\merchant\MchAppController.java#L35-L266)

## 应用与支付通道配置关联

应用与支付通道配置之间存在紧密的关联关系，这种关系通过多个服务类和数据库表实现。当应用被删除时，系统会自动清理相关的支付通道和支付参数配置，确保数据的一致性。

### 关联关系分析

1. **MchPayPassage**: 应用与支付通道的关联表，通过appId字段与MchApp关联。当删除应用时，系统会删除所有关联的支付通道记录。

2. **PayInterfaceConfig**: 应用与支付参数配置的关联表，通过infoId字段与MchApp关联，infoType字段标记为CS.INFO_TYPE_MCH_APP。删除应用时，会清理对应的应用支付参数。

3. **PayOrder**: 支付订单表通过appId字段与MchApp关联。系统在删除应用前会检查是否存在交易数据，如果有则禁止删除，确保业务数据的完整性。

### 级联删除逻辑

`MchAppService.removeByAppId()`方法实现了完整的级联删除逻辑：

1. 检查应用是否存在交易数据，如果有则抛出异常
2. 删除应用关联的所有支付通道
3. 删除应用配置的所有支付参数
4. 最后删除应用本身

这种设计确保了数据的完整性和一致性，防止出现孤立的配置数据。

```mermaid
erDiagram
MchApp ||--o{ MchPayPassage : "1对多"
MchApp ||--o{ PayInterfaceConfig : "1对多"
MchApp ||--o{ PayOrder : "1对多"
MchApp {
string appId PK
string appName
string mchNo FK
byte state
string appSecret
datetime createdAt
datetime updatedAt
}
MchPayPassage {
long id PK
string appId FK
string wayCode
string ifCode
decimal rate
byte state
}
PayInterfaceConfig {
long id PK
string infoId FK
byte infoType
string ifCode
string ifParams
decimal ifRate
byte state
}
PayOrder {
string payOrderId PK
string appId FK
string mchNo
string mchOrderNo
long amount
byte state
}
```

**Diagram sources**
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L34-L56)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)

**Section sources**
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L34-L56)

## 应用创建与配置流程示例

以下是一个完整的应用创建与配置流程示例，展示了如何通过API接口创建应用并进行相关配置。

### 创建应用

```java
// 创建应用请求示例
{
  "appName": "测试应用",
  "appSecret": "your_app_secret_here",
  "mchNo": "MCH123456",
  "remark": "这是一个测试应用",
  "state": 1
}
```

### 配置支付通道

创建应用后，可以通过支付通道配置接口为应用配置支持的支付方式：

```java
// 支付通道配置请求示例
{
  "appId": "APP123456",
  "wayCode": "WX_PAY",
  "ifCode": "WX_NATIVE",
  "rate": 0.006,
  "state": 1
}
```

### 配置支付参数

为应用配置具体的支付参数，如支付宝或微信支付所需的密钥等：

```java
// 支付参数配置请求示例
{
  "infoId": "APP123456",
  "ifCode": "ALIPAY",
  "ifParams": "{\"appPrivateKey\":\"your_private_key\",\"alipayPublicKey\":\"alipay_public_key\"}",
  "ifRate": 0.006,
  "state": 1
}
```

### 使用应用进行支付

配置完成后，可以使用该应用发起支付请求：

```java
// 支付请求示例
{
  "mchNo": "MCH123456",
  "appId": "APP123456",
  "mchOrderNo": "ORDER001",
  "wayCode": "WX_PAY",
  "amount": 100,
  "subject": "测试商品",
  "notifyUrl": "https://yourdomain.com/notify"
}
```

在整个流程中，系统会自动验证应用的有效性、商户状态以及支付参数的完整性，确保交易的安全性。

**Section sources**
- [MchAppController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchAppController.java#L68-L93)
- [MchPayPassageConfigController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchPayPassageConfigController.java#L149-L176)
- [MchPayInterfaceConfigController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchPayInterfaceConfigController.java#L113-L169)

## 应用管理模块核心作用

应用管理模块在商户系统中扮演着至关重要的角色，其核心作用体现在以下几个方面：

### 统一身份管理

应用管理模块为每个商户应用提供了唯一的身份标识（appId）和认证密钥（appSecret），实现了API调用的统一身份验证。所有对外的API请求都需要提供有效的appId和签名，系统通过验证appSecret来确认请求的合法性，确保了接口调用的安全性。

### 权限隔离

通过应用与商户的绑定关系，实现了不同商户之间的数据隔离。每个应用只能访问和操作自己商户范围内的数据，防止了数据越权访问。同时，系统管理端、商户端和代理商端的权限设计，确保了不同角色只能执行其权限范围内的操作。

### 配置中心

应用管理模块作为系统的配置中心，集中管理了应用的基本信息、支付通道配置和支付参数配置。当应用配置发生变化时，系统会通过消息队列（ResetIsvMchAppInfoConfigMQ）广播配置更新消息，通知所有相关节点刷新缓存，确保配置的实时性和一致性。

### 业务扩展

应用管理模块的设计支持灵活的业务扩展。通过应用与支付通道、支付参数的关联关系，可以方便地为应用添加新的支付方式或修改现有配置，而无需修改核心业务逻辑。这种松耦合的设计使得系统能够快速适应业务需求的变化。

### 安全保障

模块实现了多层次的安全保障机制，包括应用密钥的脱敏显示、API调用的签名验证、操作权限的细粒度控制等。这些安全措施有效防止了密钥泄露、未授权访问和数据篡改等安全风险，确保了系统的稳定运行。

**Section sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchAppController.java](file://sys-manager\src\main\java\com\unipay\mgr\ctrl\merchant\MchAppController.java#L30-L169)
- [ResetIsvMchAppInfoConfigMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\ResetIsvMchAppInfoConfigMQ.java#L16-L83)