# 消息队列设计

<cite>
**本文档引用文件**   
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java)
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [MQVenderCS.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQVenderCS.java)
- [MQSendTypeEnum.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQSendTypeEnum.java)
- [ActiveMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/activemq/ActiveMQSender.java)
- [RabbitMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rabbitmq/RabbitMQSender.java)
- [RocketMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/rocketmq/RocketMQSender.java)
- [AliYunRocketMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/aliyunrocketmq/AliYunRocketMQSender.java)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [MqThreadExecutor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/executor/MqThreadExecutor.java)
</cite>

## 目录
1. [消息队列设计](#消息队列设计)
2. [消息队列核心设计](#消息队列核心设计)
3. [消息生产者与消费者抽象](#消息生产者与消费者抽象)
4. [多MQ实现支持](#多mq实现支持)
5. [关键消息类型](#关键消息类型)
6. [消息可靠性保障](#消息可靠性保障)
7. [消息顺序性与幂等性](#消息顺序性与幂等性)

## 消息队列核心设计

本系统采用消息队列技术实现系统解耦、异步处理和流量削峰。通过消息队列，系统各模块之间实现了松耦合，提高了系统的可维护性和可扩展性。异步处理机制使得耗时操作不会阻塞主业务流程，提升了系统响应速度。同时，在高并发场景下，消息队列能够有效缓冲流量，避免系统过载。

消息队列的设计遵循生产者-消费者模式，通过`IMQSender`接口定义消息发送行为，`IMQMsgReceiver`接口定义消息接收行为。所有消息类型均继承自`AbstractMQ`抽象类，确保了消息结构的一致性。系统支持多种消息类型，包括支付通知、分账指令和配置重置等，满足不同业务场景的需求。

**Section sources**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java#L7-L11)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java#L9-L20)

## 消息生产者与消费者抽象

系统通过接口抽象实现了消息生产者和消费者的统一管理。`IMQSender`接口定义了两种消息发送方式：实时发送和延迟发送。实时发送用于需要立即处理的业务场景，而延迟发送则适用于需要定时或延后处理的场景。

```mermaid
classDiagram
class IMQSender {
<<interface>>
+send(AbstractMQ mqModel) void
+send(AbstractMQ mqModel, int delay) void
}
class IMQMsgReceiver {
<<interface>>
+receiveMsg(String msg) void
}
class AbstractMQ {
<<abstract>>
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
}
IMQSender <|-- ActiveMQSender
IMQSender <|-- RabbitMQSender
IMQSender <|-- RocketMQSender
IMQSender <|-- AliYunRocketMQSender
IMQMsgReceiver <|-- PayOrderMchNotifyMQReceiver
IMQMsgReceiver <|-- PayOrderDivisionMQReceiver
IMQMsgReceiver <|-- ResetAppConfigMQReceiver
AbstractMQ <|-- PayOrderMchNotifyMQ
AbstractMQ <|-- PayOrderDivisionMQ
AbstractMQ <|-- ResetAppConfigMQ
```

**Diagram sources **
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java#L7-L11)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java#L9-L20)

**Section sources**
- [IMQSender.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQSender.java#L9-L17)
- [IMQMsgReceiver.java](file://components/components-mq/src/main/java/com/unipay/components/mq/vender/IMQMsgReceiver.java#L7-L11)
- [AbstractMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/AbstractMQ.java#L9-L20)

## 多MQ实现支持

系统通过工厂模式实现了对多种消息队列中间件的支持，包括ActiveMQ、RabbitMQ、RocketMQ和阿里云RocketMQ。通过配置文件中的`isys.mq.vender`参数，可以灵活切换不同的MQ实现，无需修改业务代码。

```mermaid
classDiagram
class MQVenderCS {
+YML_VENDER_KEY String
+ACTIVE_MQ String
+RABBIT_MQ String
+ROCKET_MQ String
+ALIYUN_ROCKET_MQ String
}
class MQSendTypeEnum {
+QUEUE
+BROADCAST
}
MQVenderCS --> MQSendTypeEnum : "定义"
```

**Diagram sources **
- [MQVenderCS.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQVenderCS.java#L7-L16)
- [MQSendTypeEnum.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQSendTypeEnum.java#L8-L13)

**Section sources**
- [MQVenderCS.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQVenderCS.java#L7-L16)
- [MQSendTypeEnum.java](file://components/components-mq/src/main/java/com/unipay/components/mq/constant/MQSendTypeEnum.java#L8-L13)

## 关键消息类型

系统定义了多种关键业务消息类型，每种消息类型都有明确的业务语义和处理流程。

### 支付通知消息(PayOrderMchNotifyMQ)

支付通知消息用于向商户系统推送支付结果。该消息采用点对点(QUEUE)模式发送，确保消息被准确送达。消息体包含通知单号，消费者接收到消息后会向商户配置的回调地址发送HTTP请求。

```mermaid
sequenceDiagram
participant PaymentSystem as 支付系统
participant MQ as 消息队列
participant MerchantSystem as 商户系统
PaymentSystem->>MQ : 发送PayOrderMchNotifyMQ
MQ->>MerchantSystem : 推送支付通知
MerchantSystem->>MerchantSystem : 处理通知
MerchantSystem-->>MQ : 返回处理结果
alt 通知成功
MQ-->>PaymentSystem : 标记通知成功
else 通知失败
MQ->>MQ : 延迟重试(30,60,90...秒)
MQ->>MerchantSystem : 重新推送
end
```

**Diagram sources **
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L23-L98)

**Section sources**
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L23-L98)

### 分账指令消息(PayOrderDivisionMQ)

分账指令消息用于触发订单分账流程。该消息同样采用点对点模式，包含支付订单号、是否使用默认分组、分账接收者列表等信息。分账接收者可以是具体的接收者ID或接收者组ID。

```mermaid
classDiagram
class PayOrderDivisionMQ {
+MQ_NAME String
+payload MsgPayload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(payOrderId, useSysAutoDivisionReceivers, receiverList) PayOrderDivisionMQ
+build(payOrderId, useSysAutoDivisionReceivers, receiverList, isResend) PayOrderDivisionMQ
+parse(msg) MsgPayload
}
class MsgPayload {
+payOrderId String
+useSysAutoDivisionReceivers Byte
+receiverList CustomerDivisionReceiver[]
+isResend Boolean
}
class CustomerDivisionReceiver {
+receiverId Long
+receiverGroupId Long
+divisionProfit BigDecimal
}
PayOrderDivisionMQ --> MsgPayload
MsgPayload --> CustomerDivisionReceiver
```

**Diagram sources **
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)

**Section sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L15-L33)

### 配置重置消息(ResetAppConfigMQ)

配置重置消息采用广播(BROADCAST)模式，用于通知所有系统节点重新加载配置。当系统配置发生变化时，发送此消息，所有订阅该消息的节点都会收到通知并执行配置重置操作。

```mermaid
flowchart TD
A[配置变更] --> B[发送ResetAppConfigMQ]
B --> C{广播模式}
C --> D[sys-payment节点]
C --> E[sys-manager节点]
C --> F[sys-merchant节点]
C --> G[sys-agent节点]
D --> H[重置配置]
E --> I[重置配置]
F --> J[重置配置]
G --> K[重置配置]
```

**Diagram sources **
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)

**Section sources**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)

## 消息可靠性保障

为确保消息的可靠传递，系统在多个层面实施了保障措施。首先，所有MQ实现类都配置了消息持久化，确保在服务重启后消息不会丢失。其次，系统采用了ACK机制，消费者必须显式确认消息处理成功，否则消息会重新入队。

对于支付通知这类关键业务，系统实现了多级重试机制。当首次通知失败时，会按照30秒、60秒、90秒等递增的间隔进行延迟重试，最多尝试6次。同时，系统配置了专用的线程池处理支付通知，避免因线程资源不足导致消息积压。

```mermaid
flowchart TD
A[发送消息] --> B[持久化存储]
B --> C[投递给消费者]
C --> D{处理成功?}
D --> |是| E[ACK确认]
D --> |否| F[加入重试队列]
F --> G[延迟重试]
G --> C
E --> H[消息完成]
```

**Diagram sources **
- [MqThreadExecutor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/executor/MqThreadExecutor.java#L15-L42)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)

**Section sources**
- [MqThreadExecutor.java](file://components/components-mq/src/main/java/com/unipay/components/mq/executor/MqThreadExecutor.java#L15-L42)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)

## 消息顺序性与幂等性

系统通过多种机制保证消息的顺序性和幂等性。对于需要保证顺序的业务场景，如分账处理，系统采用单队列单消费者模式，确保消息按发送顺序被处理。同时，通过合理设计消息体结构，避免了因消息乱序导致的业务逻辑错误。

幂等性处理是系统设计的重要原则。所有消息处理方法都设计为幂等操作，即使同一条消息被多次消费，也不会产生副作用。这通过在消息处理前检查业务状态实现，例如在处理支付通知时，会先检查通知记录的状态，避免重复通知。

```mermaid
flowchart TD
A[接收消息] --> B[检查业务状态]
B --> C{是否已处理?}
C --> |是| D[忽略消息]
C --> |否| E[处理业务逻辑]
E --> F[更新状态]
F --> G[ACK确认]
```

**Diagram sources **
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L21-L31)

**Section sources**
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L34-L97)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L21-L31)