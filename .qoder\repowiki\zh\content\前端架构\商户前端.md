
# 商户前端

<cite>
**本文档引用文件**   
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)
- [PayOrderDivisionRecordController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\PayOrderDivisionRecordController.java)
- [PaytestController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\paytest\PaytestController.java)
- [MchPayPassageConfigController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchPayPassageConfigController.java)
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java)
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java)
- [PayWayController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\payconfig\PayWayController.java)
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java)
- [PayWay.java](file://core\src\main\java\com\unipay\core\entity\PayWay.java)
- [MchDivisionReceiverService.java](file://service\src\main\java\com\unipay\service\impl\MchDivisionReceiverService.java)
- [MchDivisionReceiverGroupService.java](file://service\src\main\java\com\unipay\service\impl\MchDivisionReceiverGroupService.java)
- [PayOrderDivisionRecordService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderDivisionRecordService.java)
- [MchPayPassageService.java](file://service\src\main\java\com\unipay\service\impl\MchPayPassageService.java)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java)
- [PayWayService.java](file://service\src\main\java\com\unipay\service\impl\PayWayService.java)
- [MchDivisionReceiverMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchDivisionReceiverMapper.java)
- [MchDivisionReceiverGroupMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchDivisionReceiverGroupMapper.java)
- [PayOrderDivisionRecordMapper.java](file://service\src\main\java\com\unipay\service\mapper\PayOrderDivisionRecordMapper.java)
- [MchAppMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchAppMapper.java)
- [PayWayMapper.java](file://service\src\main\java\com\unipay\service\mapper\PayWayMapper.java)
</cite>

## 目录
1. [商户前端应用概述](#商户前端应用概述)
2. [核心功能模块详解](#核心功能模块详解)
   - [应用管理](#应用管理)
   - [支付配置](#支付配置)
   - [订单查询](#订单查询)
   - [分账管理](#分账管理)
   - [支付测试工具](#支付测试工具)
3. [UI框架与设计理念](#ui框架与设计理念)
4. [分账模块实现细节](#分账模块实现细节)
5. [支付通道配置实现](#支付通道配置实现)
6. [与支付网关API集成最佳实践](#与支付网关api集成最佳实践)
7. [使用测试工具验证支付流程](#使用测试工具验证支付流程)

## 商户前端应用概述

商户前端应用是为商户提供的日常操作界面，集成了应用管理、支付配置、订单查询、分账管理和支付测试等核心功能。该应用采用与运营平台相似的UI框架，但在功能范围上有所限制，专注于商户日常运营所需的核心功能。系统通过统一的权限控制和数据隔离机制，确保商户只能访问和操作其自身的数据，保障了系统的安全性和数据的私密性。

**Section sources**
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [PayWayController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\payconfig\PayWayController.java#L29-L71)

## 核心功能模块详解

### 应用管理

应用管理模块允许商户创建、查看、更新和删除其应用。每个应用都有唯一的应用ID和应用密钥，用于API调用的身份验证。商户可以为不同的业务场景创建多个应用，并独立管理每个应用的配置和权限。

```mermaid
classDiagram
class MchApp {
+String appId
+String appName
+String mchNo
+Byte state
+String appSecret
+String remark
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
MchAppController --> MchApp : "使用"
MchAppService --> MchApp : "管理"
MchAppMapper --> MchApp : "持久化"
```

**Diagram sources**
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L27-L96)
- [MchAppMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchAppMapper.java#L13-L15)

**Section sources**
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [MchApp.java](file://core\src\main\java\com\unipay\core\entity\MchApp.java#L22-L98)
- [MchAppService.java](file://service\src\main\java\com\unipay\service\impl\MchAppService.java#L27-L96)

### 支付配置

支付配置模块允许商户为其应用配置支持的支付方式和支付通道。商户可以查看可用的支付接口，并根据需要启用或禁用特定的支付通道。系统支持多种支付方式，包括微信支付、支付宝、PayPal等。

```mermaid
classDiagram
class PayWay {
+String wayCode
+String wayName
+Date createdAt
+Date updatedAt
}
class MchPayPassage {
+Long id
+String mchNo
+String appId
+String ifCode
+String wayCode
+BigDecimal rate
+String riskConfig
+Byte state
+Date createdAt
+Date updatedAt
}
PayWayController --> PayWay : "使用"
MchPayPassageConfigController --> MchPayPassage : "管理"
PayWayService --> PayWay : "管理"
MchPayPassageService --> MchPayPassage : "管理"
PayWayMapper --> PayWay : "持久化"
MchPayPassageMapper --> MchPayPassage : "持久化"
```

**Diagram sources**
- [PayWay.java](file://core\src\main\java\com\unipay\core\entity\PayWay.java#L23-L62)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)
- [PayWayController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\payconfig\PayWayController.java#L29-L71)
- [MchPayPassageConfigController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchPayPassageConfigController.java#L40-L186)
- [PayWayService.java](file://service\src\main\java\com\unipay\service\impl\PayWayService.java#L16-L19)
- [MchPayPassageService.java](file://service\src\main\java\com\unipay\service\impl\MchPayPassageService.java#L29-L124)
- [PayWayMapper.java](file://service\src\main\java\com\unipay\service\mapper\PayWayMapper.java#L14-L16)
- [MchPayPassageMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchPayPassageMapper.java#L13-L15)

**Section sources**
- [MchPayPassageConfigController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchPayPassageConfigController.java#L40-L186)
- [PayWayController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\payconfig\PayWayController.java#L29-L71)
- [PayWay.java](file://core\src\main\java\com\unipay\core\entity\PayWay.java#L23-L62)
- [MchPayPassage.java](file://core\src\main\java\com\unipay\core\entity\MchPayPassage.java#L25-L99)

### 订单查询

订单查询模块提供商户支付订单的查询功能。商户可以根据订单号、时间范围、支付状态等条件查询订单，并查看订单的详细信息。系统支持分页查询，便于处理大量订单数据。

```mermaid
classDiagram
class PayOrder {
+String payOrderId
+String mchNo
+String isvNo
+String appId
+String mchName
+Byte mchType
+String mchOrderNo
+String ifCode
+String wayCode
+Long amount
+BigDecimal mchFeeRate
+Long mchFeeAmount
+String currency
+Byte state
+Byte notifyState
+String clientIp
+String subject
+String body
+String channelExtra
+String channelUser
+String channelOrderNo
+Byte refundState
+Integer refundTimes
+Long refundAmount
+Byte divisionMode
+Byte divisionState
+Date divisionLastTime
+String errCode
+String errMsg
+String extParam
+String notifyUrl
+String returnUrl
+Date expiredTime
+Date successTime
+Date createdAt
+Date updatedAt
}
PayOrderController --> PayOrder : "使用"
PayOrderService --> PayOrder : "管理"
PayOrderMapper --> PayOrder : "持久化"
```

**Diagram sources**
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java#L46-L192)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java#L36-L640)
- [PayOrderMapper.java](file://service\src\main\java\com\unipay\service\mapper\PayOrderMapper.java#L14-L16)

**Section sources**
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java#L46-L192)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java#L24-L278)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java#L36-L640)

### 分账管理

分账管理模块是商户前端的核心功能之一，包括分账接收方管理、分账账号组管理和分账记录查询。商户可以创建分账账号组，将分账接收方分配到不同的组，并配置自动分账规则。

```mermaid
classDiagram
class MchDivisionReceiver {
+Long receiverId
+String receiverAlias
+Long receiverGroupId
+String receiverGroupName
+String mchNo
+String isvNo
+String appId
+String ifCode
+Byte accType
+String accNo
+String accName
+String relationType
+String relationTypeName
+BigDecimal divisionProfit
+Byte state
+String channelBindResult
+String channelExtInfo
+Date bindSuccessTime
+Date createdAt
+Date updatedAt
}
class MchDivisionReceiverGroup {
+Long receiverGroupId
+String receiverGroupName
+String mchNo
+Byte autoDivisionFlag
+Long createdUid
+String createdBy
+Date createdAt
+Date updatedAt
}
class PayOrderDivisionRecord {
+Long recordId
+String mchNo
+String isvNo
+String appId
+String mchName
+Byte mchType
+String ifCode
+String payOrderId
+String payOrderChannelOrderNo
+Long payOrderAmount
+Long payOrderDivisionAmount
+String batchOrderId
+String channelBatchOrderId
+Byte state
+String channelRespResult
+Long receiverId
+Long receiverGroupId
+String receiverAlias
+Byte accType
+String accNo
+String accName
+String relationType
+String relationTypeName
+BigDecimal divisionProfit
+Long calDivisionAmount
+Date createdAt
+Date updatedAt
}
MchDivisionReceiverController --> MchDivisionReceiver : "使用"
MchDivisionReceiverGroupController --> MchDivisionReceiverGroup : "使用"
PayOrderDivisionRecordController --> PayOrderDivisionRecord : "使用"
MchDivisionReceiverService --> MchDivisionReceiver : "管理"
MchDivisionReceiverGroupService --> MchDivisionReceiverGroup : "管理"
PayOrderDivisionRecordService --> PayOrderDivisionRecord : "管理"
MchDivisionReceiverMapper --> MchDivisionReceiver : "持久化"
MchDivisionReceiverGroupMapper --> MchDivisionReceiverGroup : "持久化"
PayOrderDivisionRecordMapper --> PayOrderDivisionRecord : "持久化"
```

**Diagram sources**
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java#L23-L159)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java#L22-L87)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java#L23-L205)
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java#L46-L246)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java#L36-L188)
- [PayOrderDivisionRecordController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\PayOrderDivisionRecordController.java#L34-L160)
- [MchDivisionReceiverService.java](file://service\src\main\java\com\unipay\service\impl\MchDivisionReceiverService.java#L15-L18)
- [MchDivisionReceiverGroupService.java](file://service\src\main\java\com\unipay\service\impl\MchDivisionReceiverGroupService.java#L15-L24)
- [PayOrderDivisionRecordService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderDivisionRecordService.java#L24-L88)
- [MchDivisionReceiverMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchDivisionReceiverMapper.java#L13-L15)
- [MchDivisionReceiverGroupMapper.java](file://service\src\main\java\com\unipay\service\mapper\MchDivisionReceiverGroupMapper.java#L13-L15)
- [PayOrderDivisionRecordMapper.java](file://service\src\main\java\com\unipay\service\mapper\PayOrderDivisionRecordMapper.java#L16-L24)

**Section sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java#L46-L246)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java#L36-L188)
- [PayOrderDivisionRecordController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\PayOrderDivisionRecordController.java#L34-L160)
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java#L23-L159)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java#L22-L87)
- [PayOrderDivisionRecord.java](file://core\src\main\java\com\unipay\core\entity\PayOrderDivisionRecord.java#L23-L205)

### 支付测试工具

支付测试工具模块为商户提供了一个便捷的支付流程测试环境。商户可以使用该工具模拟支付请求，验证支付流程的正确性，并查看支付结果。该工具支持多种支付方式和支付场景。

```mermaid
sequenceDiagram
participant 商户前端
participant PaytestController
participant JeepayClient
participant 支付网关
商户前端->>PaytestController : 发送支付测试请求
PaytestController->>PaytestController : 验证请求参数
PaytestController->>JeepayClient : 构建支付请求
JeepayClient->>支付网关 : 发送支付请求
支付网关-->>JeepayClient : 返回支付响应
JeepayClient-->>PaytestController : 处理支付响应
PaytestController-->>商户前端 : 返回支付结果
```

**Diagram sources**
- [PaytestController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\paytest\PaytestController.java#L36-L160)

**Section sources**
- [PaytestController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\paytest\PaytestController.java#L36-L160)

## UI框架与设计理念

商户前端应用采用了与运营平台相似的UI框架，确保了用户在不同平台间切换时的操作一致性。然而，功能范围上有所限制，仅包含商户日常运营所需的核心功能，如应用管理、支付配置、订单查询、分账管理和支付测试。这种设计理念既保证了用户体验的一致性，又避免了功能冗余，提高了系统的易用性和效率。

**Section sources**
- [MchAppController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchAppController.java#L30-L174)
- [MchPayPassageConfigController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\merchant\MchPayPassageConfigController.java#L40-L186)
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java#L46-L192)
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java#L46-L246)
- [PaytestController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\paytest\PaytestController.java#L36-L160)

## 分账模块实现细节

分账模块的实现基于三个核心实体：分账接收方（MchDivisionReceiver）、分账账号组（MchDivisionReceiverGroup）和分账记录（PayOrderDivisionRecord）。分账接收