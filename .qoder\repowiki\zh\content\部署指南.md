# 部署指南

<cite>
**本文档引用文件**   
- [deploy.sh](file://z-docs/deploy/deploy.sh)
- [nginx-unipay.conf](file://z-docs/deploy/nginx-unipay.conf)
- [init_database.sh](file://z-docs/script/init_database.sh)
- [init.sql](file://z-docs/sql/init.sql)
- [start_tar.sh](file://start_tar.sh)
- [pom.xml](file://pom.xml)
- [sys-payment/pom.xml](file://sys-payment/pom.xml)
- [sys-manager/pom.xml](file://sys-manager/pom.xml)
- [sys-merchant/pom.xml](file://sys-merchant/pom.xml)
- [sys-agent/pom.xml](file://sys-agent/pom.xml)
</cite>

## 目录
1. [引言](#引言)
2. [服务器环境准备](#服务器环境准备)
3. [数据库初始化](#数据库初始化)
4. [后端服务打包与部署](#后端服务打包与部署)
5. [前端UI构建与发布](#前端ui构建与发布)
6. [Nginx反向代理配置](#nginx反向代理配置)
7. [部署脚本使用说明](#部署脚本使用说明)
8. [监控与故障排查建议](#监控与故障排查建议)
9. [结论](#结论)

## 引言

本指南旨在为运维人员提供从零开始搭建UniPay统一支付平台生产环境的完整步骤。文档涵盖了服务器环境准备、数据库初始化、后端服务打包与部署、前端UI构建与发布以及Nginx反向代理配置等关键环节。同时，本文档详细解释了不同部署脚本的使用场景，并为系统监控、日志收集和故障排查提供了初步建议，确保系统能够稳定、高效地运行。

## 服务器环境准备

在部署UniPay系统之前，必须先准备好服务器的基础运行环境。该系统基于Java技术栈构建，需要安装以下核心组件：

### JDK安装

UniPay系统要求Java 17或更高版本。可通过以下命令检查当前Java版本：
```bash
java -version
```

若未安装或版本不符，建议从Oracle官网或OpenJDK获取Java 17安装包。对于基于Debian/Ubuntu的系统，可使用以下命令安装：
```bash
sudo apt update
sudo apt install openjdk-17-jdk
```

对于基于CentOS/RHEL的系统，可使用：
```bash
sudo yum install java-17-openjdk-devel
```

安装完成后，确保`JAVA_HOME`环境变量已正确设置。

### MySQL数据库安装

系统使用MySQL作为持久化存储。建议安装MySQL 8.0版本。安装步骤如下：

1.  **添加MySQL APT仓库**（以Ubuntu为例）：
    ```bash
    wget https://dev.mysql.com/get/mysql-apt-config_0.8.24-1_all.deb
    sudo dpkg -i mysql-apt-config_0.8.24-1_all.deb
    ```
    在配置界面中选择MySQL 8.0。

2.  **安装MySQL Server**：
    ```bash
    sudo apt update
    sudo apt install mysql-server
    ```

3.  **安全配置**：
    ```bash
    sudo mysql_secure_installation
    ```
    按提示设置root密码、移除匿名用户、禁止root远程登录等。

4.  **启动并启用服务**：
    ```bash
    sudo systemctl start mysql
    sudo systemctl enable mysql
    ```

### Redis安装

Redis用于缓存和会话管理。安装步骤如下：

```bash
# 下载并编译Redis（以6.2版本为例）
wget http://download.redis.io/releases/redis-6.2.6.tar.gz
tar xzf redis-6.2.6.tar.gz
cd redis-6.2.6
make
sudo make install

# 启动Redis服务
redis-server --daemonize yes
```

### 消息队列（MQ）安装

系统支持多种消息队列，如ActiveMQ、RabbitMQ、RocketMQ等。以RabbitMQ为例：

1.  **安装Erlang**（RabbitMQ依赖）：
    ```bash
    sudo apt install erlang
    ```

2.  **安装RabbitMQ**：
    ```bash
    wget https://github.com/rabbitmq/rabbitmq-server/releases/download/v3.9.13/rabbitmq-server_3.9.13-1_all.deb
    sudo dpkg -i rabbitmq-server_3.9.13-1_all.deb
    ```

3.  **启动服务**：
    ```bash
    sudo systemctl start rabbitmq-server
    sudo systemctl enable rabbitmq-server
    ```

4.  **启用管理插件**（可选，便于监控）：
    ```bash
    sudo rabbitmq-plugins enable rabbitmq_management
    ```

完成上述组件的安装后，确保所有服务均能正常启动并监听在指定端口。

**Section sources**
- [pom.xml](file://pom.xml#L22)
- [sys-payment/pom.xml](file://sys-payment/pom.xml#L10)
- [sys-manager/pom.xml](file://sys-manager/pom.xml#L10)
- [sys-merchant/pom.xml](file://sys-merchant/pom.xml#L10)
- [sys-agent/pom.xml](file://sys-agent/pom.xml#L10)

## 数据库初始化

数据库初始化是部署过程中的关键一步，它负责创建数据库、用户并导入初始表结构和数据。

### 使用init_database.sh脚本

`z-docs/script/init_database.sh`脚本提供了一键式数据库初始化功能。

1.  **修改脚本配置**：打开`init_database.sh`文件，根据实际环境修改以下变量：
    ```bash
    MYSQL_HOST="*************"  # 修改为您的MySQL服务器IP
    MYSQL_PORT="3307"           # 修改为您的MySQL端口
    MYSQL_ROOT_USER="root"      # MySQL root用户名
    MYSQL_ROOT_PASSWORD="LHX.0618" # MySQL root密码
    DB_NAME="jeepaydb"          # 目标数据库名
    DB_USER="jeepay"            # 应用数据库用户名
    DB_PASSWORD="123456"        # 应用数据库用户密码
    ```

2.  **执行脚本**：确保`init.sql`文件存在，然后运行脚本。
    ```bash
    cd z-docs/script
    chmod +x init_database.sh
    ./init_database.sh
    ```
    脚本会自动创建数据库`jeepaydb`、用户`jeepay`，并授予其所有权限，最后执行`init.sql`文件中的SQL语句。

### 手动初始化

如果选择手动初始化，步骤如下：

1.  **登录MySQL**：
    ```bash
    mysql -h ************* -P 3307 -u root -p
    ```

2.  **创建数据库和用户**：
    ```sql
    CREATE DATABASE jeepaydb DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    CREATE USER 'jeepay'@'%' IDENTIFIED BY '123456';
    GRANT ALL PRIVILEGES ON jeepaydb.* TO 'jeepay'@'%';
    FLUSH PRIVILEGES;
    ```

3.  **导入SQL文件**：
    ```bash
    mysql -h ************* -P 3307 -u jeepay -p jeepaydb < z-docs/sql/init.sql
    ```
    输入密码后，`init.sql`文件将被执行，创建所有表结构并插入初始数据。

**Section sources**
- [init_database.sh](file://z-docs/script/init_database.sh#L1-L57)
- [init.sql](file://z-docs/sql/init.sql#L1-L1053)

## 后端服务打包与部署

UniPay后端由多个微服务模块组成，使用Maven进行打包，通过`start_tar.sh`脚本进行部署。

### 服务模块概述

根据`pom.xml`文件中的`<modules>`定义，主要后端服务包括：
-   `sys-payment`: 统一支付网关
-   `sys-manager`: 运营平台管理端
-   `sys-merchant`: 商户平台管理端
-   `sys-agent`: 代理商平台管理端

### 使用Maven打包

1.  **进入项目根目录**：
    ```bash
    cd /path/to/uni-pay
    ```

2.  **执行打包命令**：
    ```bash
    mvn clean package -Dmaven.test.skip=true
    ```
    此命令会清理旧的构建文件，跳过测试，并为所有模块打包。成功后，每个模块的`target`目录下将生成对应的JAR包。

### 使用deploy.sh脚本部署

`z-docs/deploy/deploy.sh`是一个用于部署前端门户的自动化脚本，但其流程和思想也适用于后端服务的部署逻辑。

1.  **脚本功能**：该脚本会创建备份、安装Nginx依赖、部署前端文件、配置Nginx并启动服务。
2.  **执行**：确保以root权限运行。
    ```bash
    cd z-docs/deploy
    chmod +x deploy.sh
    ./deploy.sh
    ```

### 使用start_tar.sh脚本启动服务

`start_tar.sh`是启动后端JAR包的核心脚本。

1.  **脚本功能**：该脚本支持启动、停止、查看状态和查看日志等多种操作。
2.  **启动服务示例**：
    ```bash
    # 启动支付网关
    ./start_tar.sh -m payment
    
    # 生产环境启动运营平台
    ./start_tar.sh -m manager -p prod
    
    # 指定端口启动商户平台
    ./start_tar.sh -m merchant -P 9218
    
    # 查看所有服务状态
    ./start_tar.sh -s
    
    # 查看支付网关日志
    ./start_tar.sh -m payment --log
    ```
    脚本会自动查找`target`目录下的JAR文件，使用`nohup`在后台启动，并将PID和日志记录在`target`目录中。

**Section sources**
- [deploy.sh](file://z-docs/deploy/deploy.sh#L1-L315)
- [start_tar.sh](file://start_tar.sh#L1-L463)

## 前端UI构建与发布

前端UI位于`unipay-web-ui`目录下，包含多个子项目。

### 构建前端项目

1.  **进入前端目录**：
    ```bash
    cd unipay-web-ui
    ```

2.  **安装依赖**：
    ```bash
    # 以unipay-ui-manager为例
    cd unipay-ui-manager
    npm install
    ```

3.  **构建生产包**：
    ```bash
    npm run build
    ```
    构建完成后，会在`dist`目录下生成静态文件。

### 发布前端文件

将构建好的`dist`目录中的所有文件复制到Nginx的Web根目录下，例如`/www/wwwroot/unipay-portal`。`deploy.sh`脚本已经自动化了此过程。

## Nginx反向代理配置

Nginx作为反向代理服务器，将外部请求转发到内部的后端服务。

### nginx-unipay.conf配置详解

`z-docs/deploy/nginx-unipay.conf`文件定义了多个`upstream`和`server`块。

1.  **上游服务器定义**：
    ```nginx
    upstream unipay_manager {
        server 127.0.0.1:8080;
    }
    upstream unipay_payment {
        server 127.0.0.1:9216;
    }
    # ... 其他服务
    ```
    这里定义了后端服务的地址和端口。

2.  **服务器块配置**：
    ```nginx
    server {
        listen 80;
        server_name manager.unipay.com;
        
        location / {
            proxy_pass http://unipay_manager;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            # ... 其他proxy_set_header指令
        }
    }
    ```
    当请求`manager.unipay.com`时，Nginx会将其代理到`unipay_manager`上游，即`127.0.0.1:8080`。

3.  **静态资源缓存**：配置了对JS、CSS、图片等静态资源的长期缓存，提升性能。

4.  **HTTPS配置**：文件末尾提供了HTTPS配置的示例，生产环境应启用。

### 配置部署

1.  将`nginx-unipay.conf`文件复制到Nginx的配置目录，如`/etc/nginx/sites-available/`。
2.  创建软链接到`/etc/nginx/sites-enabled/`以启用站点。
3.  测试配置并重启Nginx：
    ```bash
    nginx -t
    systemctl restart nginx
    ```

```mermaid
graph TB
Client[客户端] --> Nginx[Nginx 反向代理]
Nginx --> Manager[运营平台 8080]
Nginx --> Merchant[商户平台 9218]
Nginx --> Agent[代理商平台 9219]
Nginx --> Payment[支付网关 9216]
Nginx --> Game[游戏服务 8088]
```

**Diagram sources**
- [nginx-unipay.conf](file://z-docs/deploy/nginx-unipay.conf#L1-L217)

**Section sources**
- [nginx-unipay.conf](file://z-docs/deploy/nginx-unipay.conf#L1-L217)

## 部署脚本使用说明

### deploy.sh

-   **用途**：主要用于部署`unipay-portal`前端门户，自动化完成从备份、依赖安装到服务启动的全过程。
-   **场景**：适用于首次部署或需要完整重置前端环境的场景。
-   **注意**：该脚本默认配置了`ybdl.shop`域名，需根据实际情况修改。

### start_tar.sh

-   **用途**：用于管理后端Java服务的生命周期，包括启动、停止、查看状态和日志。
-   **场景**：
    -   `./start_tar.sh -m payment`: 启动支付网关。
    -   `./start_tar.sh -s`: 查看所有服务状态，用于日常巡检。
    -   `./start_tar.sh -m payment --log`: 查看支付网关日志，用于故障排查。
    -   `./start_tar.sh -k`: 杀掉所有服务，用于紧急维护或重启。
-   **优势**：提供了灵活的参数，支持指定环境、端口和自定义配置文件，是日常运维的核心工具。

## 监控与故障排查建议

### 监控

1.  **服务状态监控**：定期使用`./start_tar.sh -s`检查所有服务的运行状态。
2.  **日志监控**：关注各服务`target/logs/startup.log`文件，可使用`tail -f`实时跟踪。
3.  **资源监控**：使用`top`、`htop`、`free -m`等命令监控CPU、内存和磁盘使用情况。
4.  **数据库监控**：监控MySQL连接数、慢查询日志等。

### 日志收集

-   所有后端服务的日志默认输出到各自`target/logs/`目录下的`startup.log`文件。
-   建议配置`logrotate`对日志文件进行轮转，防止磁盘空间被占满。
-   可考虑使用ELK（Elasticsearch, Logstash, Kibana）或类似工具进行集中式日志管理。

### 故障排查

1.  **服务无法启动**：
    -   检查`startup.log`中的错误信息。
    -   确认端口是否被占用：`netstat -tlnp | grep <port>`。
    -   检查数据库连接信息是否正确。

2.  **Nginx 502错误**：
    -   检查后端服务是否已启动。
    -   检查Nginx配置中的`proxy_pass`地址和端口是否正确。
    -   检查防火墙设置。

3.  **数据库连接失败**：
    -   检查MySQL服务是否运行。
    -   检查网络连通性。
    -   检查数据库用户名、密码和权限。

## 结论

本指南详细阐述了UniPay统一支付平台的完整部署流程。通过遵循服务器环境准备、数据库初始化、后端服务打包部署、前端构建发布以及Nginx配置等步骤，运维人员可以成功搭建一个生产级别的系统。熟练掌握`deploy.sh`和`start_tar.sh`等部署脚本，结合有效的监控和日志排查策略，将确保系统长期稳定、可靠地运行。