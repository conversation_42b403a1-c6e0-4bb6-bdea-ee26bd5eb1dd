# 转账API

<cite>
**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)
- [MchTransferController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/transfer/MchTransferController.java)
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java)
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java)
- [WxpayTransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayTransferService.java)
- [AlipayTransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayTransferService.java)
- [WxpayTransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayTransferNoticeService.java)
- [AlipayTransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayTransferNoticeService.java)
</cite>

## 目录
1. [接口概述](#接口概述)
2. [业务流程](#业务流程)
3. [实名认证与风控规则](#实名认证与风控规则)
4. [渠道限额与到账时间](#渠道限额与到账时间)
5. [异步通知机制](#异步通知机制)
6. [对账文件生成](#对账文件生成)
7. [失败重试策略](#失败重试策略)
8. [异常处理指南](#异常处理指南)
9. [API接口定义](#api接口定义)

## 接口概述

本文档详细描述了企业向个人账户转账的API接口 `POST /api/pay/transfer` 的实现机制。该接口支持多种入账方式，包括微信零钱、支付宝转账和银行卡转账，为企业提供灵活的资金结算解决方案。

**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [MchTransferController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/transfer/MchTransferController.java)

## 业务流程

企业付款到个人账户的业务流程如下：

1. 商户通过API发起转账请求
2. 系统验证商户资质和账户余额
3. 根据指定的入账方式调用相应的支付渠道接口
4. 支付渠道处理转账请求并返回结果
5. 系统更新转账订单状态
6. 通过异步通知将最终结果告知商户

```mermaid
flowchart TD
A[商户发起转账请求] --> B{验证参数}
B --> |通过| C[创建转账订单]
C --> D[调用支付渠道接口]
D --> E{渠道处理结果}
E --> |成功| F[更新订单状态为成功]
E --> |失败| G[更新订单状态为失败]
E --> |处理中| H[保持订单状态为转账中]
F --> I[发送异步通知]
G --> I
H --> J[定时查询订单状态]
J --> K{获取最终状态}
K --> |成功| F
K --> |失败| G
```

**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)

## 实名认证与风控规则

系统实施严格的实名认证和风控规则以确保资金安全：

- 所有收款账户必须完成实名认证
- 转账金额超过一定限额需要额外的身份验证
- 系统监控异常转账行为，如频繁小额转账、短时间内大额转账等
- 对高风险交易进行人工审核
- 支持商户自定义风控规则

**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java)

## 渠道限额与到账时间

不同支付渠道的限额和到账时间存在差异：

### 微信零钱
- 单笔限额：2万元
- 单日限额：20万元
- 到账时间：即时到账
- 服务费率：0.1%

### 支付宝
- 单笔限额：5万元
- 单日限额：50万元
- 到账时间：即时到账
- 服务费率：0.2%

### 银行卡
- 单笔限额：根据银行规定
- 单日限额：根据银行规定
- 到账时间：T+1工作日
- 服务费率：0.3%

```mermaid
graph TB
subgraph "渠道特性"
A[微信零钱] --> B["限额: 2万/笔<br/>费率: 0.1%"]
C[支付宝] --> D["限额: 5万/笔<br/>费率: 0.2%"]
E[银行卡] --> F["限额: 银行规定<br/>费率: 0.3%"]
end
```

**本文档引用文件**  
- [WxpayTransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayTransferService.java)
- [AlipayTransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayTransferService.java)

## 异步通知机制

系统通过异步通知机制确保商户能够及时获知转账结果：

1. 当支付渠道返回最终结果时，系统接收回调
2. 验证回调数据的签名
3. 更新本地订单状态
4. 向商户配置的`notifyUrl`发送通知
5. 商户返回成功响应
6. 如果未收到成功响应，系统将进行重试

```mermaid
sequenceDiagram
participant 渠道 as 支付渠道
participant 系统 as 支付系统
participant 商户 as 商户系统
渠道->>系统 : 发送转账结果通知
系统->>系统 : 验证签名
系统->>系统 : 更新订单状态
系统->>商户 : POST通知到notifyUrl
alt 商户返回成功
商户-->>系统 : HTTP 200
else 商户返回失败或超时
系统->>商户 : 5分钟后重试
商户-->>系统 : HTTP 200
end
```

**本文档引用文件**  
- [ITransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferNoticeService.java)
- [WxpayTransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayTransferNoticeService.java)
- [AlipayTransferNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/alipay/AlipayTransferNoticeService.java)

## 对账文件生成

系统每日自动生成对账文件，包含以下信息：

- 转账订单号
- 商户订单号
- 转账金额
- 入账方式
- 转账状态
- 转账时间
- 渠道订单号
- 服务费

对账文件格式为CSV，可通过商户后台下载，也可配置自动推送至指定FTP服务器。

**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)

## 失败重试策略

系统实施智能失败重试策略：

- 对于网络超时等临时性错误，立即重试最多3次
- 对于明确的业务错误（如余额不足），不进行重试
- 对于状态不明的交易，通过查询接口确认最终状态
- 重试间隔采用指数退避算法
- 所有重试操作均有详细日志记录

**本文档引用文件**  
- [ITransferService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/ITransferService.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)

## 异常处理指南

### 账户异常
当收款账户异常时，系统返回相应错误码：
- `ACCOUNT_NOT_EXIST`：账户不存在
- `ACCOUNT_FROZEN`：账户被冻结
- `ACCOUNT_CLOSED`：账户已关闭

### 余额不足
当商户余额不足时，系统返回`INSUFFICIENT_BALANCE`错误码，并提供当前余额信息。

### 参数错误
对于参数错误，系统返回详细的错误信息，包括：
- `INVALID_AMOUNT`：金额无效
- `MISSING_REQUIRED_FIELD`：缺少必填字段
- `INVALID_FORMAT`：格式错误

```mermaid
flowchart TD
A[转账请求] --> B{参数验证}
B --> |失败| C[返回参数错误]
B --> |通过| D{余额检查}
D --> |不足| E[返回余额不足]
D --> |充足| F[调用渠道接口]
F --> G{渠道返回}
G --> |账户异常| H[返回账户异常]
G --> |成功| I[处理成功]
G --> |其他错误| J[记录错误日志]
```

**本文档引用文件**  
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [TransferOrderService.java](file://service/src/main/java/com/unipay/service/impl/TransferOrderService.java)

## API接口定义

### POST /api/pay/transfer

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| mchOrderNo | string | 是 | 商户订单号 |
| entryType | string | 是 | 入账方式：WX_CASH-微信零钱；ALIPAY_CASH-支付宝转账；BANK_CARD-银行卡 |
| ifCode | string | 是 | 支付接口代码 |
| amount | number | 是 | 转账金额，单位元 |
| accountNo | string | 是 | 收款账号 |
| accountName | string | 否 | 收款人姓名 |
| bankName | string | 否 | 收款人开户行名称 |
| clientIp | string | 否 | 客户端IP |
| transferDesc | string | 否 | 转账备注信息 |
| notifyUrl | string | 否 | 异步通知地址 |
| channelExtra | string | 否 | 特定渠道发起时额外参数 |
| extParam | string | 否 | 扩展参数 |

#### 响应参数
| 参数名 | 类型 | 描述 |
|-------|------|------|
| code | integer | 响应码，0表示成功 |
| msg | string | 响应消息 |
| data | object | 返回数据 |

#### 状态码
| 状态码 | 描述 |
|-------|------|
| 0 | 成功 |
| 1 | 参数错误 |
| 2 | 商户不存在或不可用 |
| 3 | 余额不足 |
| 4 | 调用渠道接口异常 |

**本文档引用文件**  
- [MchTransferController.java](file://sys-merchant/src/main/java/com/unipay/mch/ctrl/transfer/MchTransferController.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)