# 代理商前端

<cite>
**本文档引用文件**  
- [main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)
- [vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [generator-routers.js](file://unipay-web-ui/unipay-ui-agent/src/router/generator-routers.js)
- [request.js](file://unipay-web-ui/unipay-ui-agent/src/http/request.js)
- [HttpRequest.js](file://unipay-web-ui/unipay-ui-agent/src/http/HttpRequest.js)
- [user.ts](file://unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts)
- [JeepayLayout.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue)
- [JeepayTable.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue)
- [appConfig.js](file://unipay-web-ui/unipay-ui-agent/src/config/appConfig.js)
</cite>

## 目录
1. [介绍](#介绍)
2. [项目结构](#项目结构)
3. [技术栈与构建配置](#技术栈与构建配置)
4. [动态路由生成机制](#动态路由生成机制)
5. [自定义组件应用](#自定义组件应用)
6. [API请求封装](#api请求封装)
7. [状态管理](#状态管理)
8. [开发者指导](#开发者指导)

## 介绍

代理商前端应用是代理商管理门户的核心界面系统，为代理商提供全面的业务管理功能。该系统支持代理商信息管理、下级商户管理、分润记录查看等核心功能，旨在为代理商提供高效、直观的管理体验。系统基于现代前端技术栈构建，具备良好的可扩展性和可维护性，能够满足代理商在日常运营中的多样化需求。

## 项目结构

代理商前端应用位于 `unipay-web-ui/unipay-ui-agent` 目录下，采用标准的Vue 3项目结构。主要目录包括：

- `src/api`：API接口定义
- `src/components`：自定义组件库
- `src/config`：全局配置
- `src/http`：HTTP请求封装
- `src/layouts`：页面布局组件
- `src/router`：路由配置
- `src/store`：状态管理
- `src/utils`：工具函数
- `src/views`：页面视图组件

该结构清晰地分离了不同类型的代码，便于团队协作和代码维护。

**Section sources**
- [main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)
- [vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)

## 技术栈与构建配置

代理商前端应用采用现代化的技术栈，包括Vue 3、TypeScript和Vite构建工具，确保了开发效率和运行性能。

### Vue 3与TypeScript

系统基于Vue 3框架构建，充分利用了Composition API的优势，使代码组织更加灵活和可复用。TypeScript的引入增强了代码的类型安全，减少了运行时错误，提高了开发体验。

### Vite构建工具配置

Vite作为构建工具，提供了快速的开发服务器启动和热模块替换功能。在 `vite.config.ts` 文件中，配置了以下关键选项：

- 开发服务器代理：将 `/api` 请求代理到 `http://localhost:9219`，解决开发环境下的跨域问题
- 路径别名：配置 `@` 别名为 `src` 目录，简化模块导入
- Ant Design Vue按需加载：通过 `unplugin-vue-components` 插件实现组件的自动导入和按需加载
- Less变量覆盖：自定义Ant Design Vue的主题变量，实现品牌化设计

```mermaid
graph TD
A[Vite构建工具] --> B[开发服务器]
A --> C[生产构建]
B --> D[热模块替换]
B --> E[代理配置]
C --> F[代码压缩]
C --> G[资源优化]
H[Vue 3] --> A
I[TypeScript] --> A
```

**Diagram sources**
- [vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)

**Section sources**
- [vite.config.ts](file://unipay-web-ui/unipay-ui-agent/vite.config.ts)
- [main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)

## 动态路由生成机制

系统通过 `generator-routers.js` 实现基于权限的动态菜单生成，确保用户只能访问其权限范围内的功能。

### 路由生成流程

1. 用户登录后，后端返回用户权限信息，包括 `allMenuRouteTree`（菜单路由树）和 `entIdList`（权限ID列表）
2. `generatorDynamicRouter` 函数读取用户权限信息
3. 根据权限信息递归生成符合用户权限的路由配置
4. 将生成的路由添加到Vue Router中

### 核心实现

`generator-routers.js` 文件中的 `generator` 函数负责将后端返回的菜单树结构转换为Vue Router所需的路由格式。该函数会：

- 根据 `componentName` 或 `entId` 映射到实际的组件
- 设置路由的路径、名称、组件和元信息
- 处理嵌套路由，递归生成子路由
- 根据 `entType` 决定是否隐藏菜单项

```mermaid
sequenceDiagram
participant 用户
participant 前端
participant 后端
用户->>前端 : 登录
前端->>后端 : 发送登录请求
后端-->>前端 : 返回用户信息和权限数据
前端->>前端 : 调用generatorDynamicRouter()
前端->>前端 : 读取allMenuRouteTree
前端->>前端 : 递归生成路由配置
前端->>前端 : 添加路由到Vue Router
前端-->>用户 : 显示个性化菜单
```

**Diagram sources**
- [generator-routers.js](file://unipay-web-ui/unipay-ui-agent/src/router/generator-routers.js)

**Section sources**
- [generator-routers.js](file://unipay-web-ui/unipay-ui-agent/src/router/generator-routers.js)
- [appConfig.js](file://unipay-web-ui/unipay-ui-agent/src/config/appConfig.js)

## 自定义组件应用

系统提供了多个自定义组件，用于统一UI风格和简化开发。

### JeepayLayout布局组件

`JeepayLayout` 是系统的主要布局组件，提供了一致的页面结构。该组件包含：

- 可折叠的侧边栏菜单
- 响应式设计，支持移动端抽屉模式
- 可插槽的头部、页脚和内容区域
- 自动适配屏幕尺寸变化

组件通过 `menuData` 属性接收菜单数据，支持动态生成菜单项，并根据权限控制菜单显示。

### JeepayTable表格组件

`JeepayTable` 是封装的高级表格组件，提供了丰富的功能：

- 列显示控制：用户可选择显示或隐藏特定列
- 列宽拖拽调整
- 自动刷新功能
- 分页、排序和过滤集成
- 插槽支持，便于自定义单元格内容

该组件通过 `reqTableDataFunc` 属性接收数据请求函数，实现了数据加载的解耦。

```mermaid
classDiagram
class JeepayLayout {
+menuData : Array
+openKeys : Array
+selectedKeys : Array
+collapsed : Boolean
+navTheme : String
+siderWidth : Number
+collapsedWidth : Number
+menuHeaderRender : Slot
+headerContentRender : Slot
+menuFooterRender : Slot
}
class JeepayTable {
+tableColumns : Array
+reqTableDataFunc : Function
+searchData : Object
+pageSize : Number
+initData : Boolean
+autoRefTableConfig : Object
+rowSelection : Object
+rowKey : String|Function
+opRow : Slot
+data-view : Slot
+bodyCell : Slot
+headerCell : Slot
}
JeepayLayout --> "contains" JeepayTable
```

**Diagram sources**
- [JeepayLayout.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue)
- [JeepayTable.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue)

**Section sources**
- [JeepayLayout.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayLayout/JeepayLayout.vue)
- [JeepayTable.vue](file://unipay-web-ui/unipay-ui-agent/src/components/JeepayTable/JeepayTable.vue)

## API请求封装

系统通过 `HttpRequest.js` 和 `request.js` 提供了统一的API请求封装，简化了HTTP请求的处理。

### 请求封装特性

- 自动添加认证令牌到请求头
- 全局加载状态管理
- 统一的错误处理
- 响应拦截和数据解包
- 会话超时自动处理

### 使用示例

```mermaid
flowchart TD
A[发起请求] --> B[HttpRequest实例]
B --> C[添加认证令牌]
B --> D[显示加载状态]
C --> E[发送请求]
E --> F{响应成功?}
F --> |是| G[返回数据]
F --> |否| H{状态码401?}
H --> |是| I[会话超时处理]
H --> |否| J[显示错误信息]
I --> K[退出登录]
J --> L[返回错误]
G --> M[关闭加载状态]
L --> M
```

**Diagram sources**
- [HttpRequest.js](file://unipay-web-ui/unipay-ui-agent/src/http/HttpRequest.js)
- [request.js](file://unipay-web-ui/unipay-ui-agent/src/http/request.js)

**Section sources**
- [HttpRequest.js](file://unipay-web-ui/unipay-ui-agent/src/http/HttpRequest.js)
- [request.js](file://unipay-web-ui/unipay-ui-agent/src/http/request.js)

## 状态管理

系统采用Pinia作为状态管理工具，替代了传统的Vuex，提供了更简洁的API和更好的TypeScript支持。

### 用户状态管理

`user.ts` 文件定义了用户相关的全局状态，包括：

- 用户基本信息（ID、姓名、头像等）
- 权限信息（权限ID列表、菜单路由树）
- 全局加载状态
- 认证令牌管理

### 核心功能

- `refUserInfo`：刷新用户信息
- `logout`：退出登录，清除令牌
- `setGlobalLoading`：设置全局加载状态
- `putToken`：存储认证令牌

状态通过 `useUserStore()` 函数在组件中使用，实现了状态的集中管理和共享。

```mermaid
classDiagram
class useUserStore {
+userInfo : Object
+globalLoading : Boolean
+putToken(token)
+refUserInfo(val)
+logout()
+setGlobalLoading(val)
}
class UserInfo {
+userId : string
+realname : string
+sysUserId : string
+avatarUrl : string
+allMenuRouteTree : Array
+entIdList : Array
+isAdmin : string
+loginUsername : string
+sysType : string
}
useUserStore --> UserInfo : "包含"
```

**Diagram sources**
- [user.ts](file://unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts)

**Section sources**
- [user.ts](file://unipay-web-ui/unipay-ui-agent/src/store/modules/user.ts)
- [main.ts](file://unipay-web-ui/unipay-ui-agent/src/main.ts)

## 开发者指导

### 界面定制

1. **主题定制**：通过修改 `vite.config.ts` 中的Less变量来自定义主题颜色
2. **布局调整**：通过 `JeepayLayout` 的属性调整布局参数，如侧边栏宽度、主题等
3. **组件扩展**：基于现有组件创建新的自定义组件，保持UI一致性

### 功能扩展

1. **新增页面**：在 `views` 目录下创建新页面组件，并在 `appConfig.js` 中注册路由
2. **权限控制**：使用 `$access(entId)` 方法在模板中控制元素的显示权限
3. **API集成**：通过 `request` 实例调用后端API，遵循统一的请求响应格式

### 最佳实践

- 使用TypeScript接口定义数据结构
- 遵循组件单一职责原则
- 利用插槽提高组件复用性
- 保持状态管理的简洁性
- 充分利用Vite的开发特性提高开发效率