#!/bin/bash

# UniPay Portal 服务监控脚本
# 监控各个服务端口和网站状态
# 版本: v1.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN="ybdl.shop"
SERVICES=(
    "nginx:80:Nginx Web服务器"
    "manager:9217:运营平台"
    "agent:9219:代理商平台"
    "merchant:9218:商户平台"
    "payment:9216:支付网关"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查端口是否监听
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":${port} " || ss -tlnp 2>/dev/null | grep -q ":${port} "; then
        return 0
    else
        return 1
    fi
}

# 检查HTTP响应
check_http() {
    local url=$1
    local timeout=${2:-10}
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout "$timeout" "$url" 2>/dev/null)
    if [[ "$http_code" =~ ^[23] ]]; then
        echo "$http_code"
        return 0
    else
        echo "$http_code"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    echo -e "${BLUE}=== 系统资源状态 ===${NC}"
    
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    echo -e "CPU使用率: ${cpu_usage}%"
    
    # 内存使用率
    local mem_info=$(free | grep Mem)
    local mem_total=$(echo $mem_info | awk '{print $2}')
    local mem_used=$(echo $mem_info | awk '{print $3}')
    local mem_usage=$((mem_used * 100 / mem_total))
    echo -e "内存使用率: ${mem_usage}%"
    
    # 磁盘使用率
    echo -e "磁盘使用率:"
    df -h | grep -E '^/dev/' | awk '{print "  " $6 ": " $5}'
    
    # 负载平均值
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    echo -e "负载平均值:${load_avg}"
    
    echo ""
}

# 检查服务状态
check_services() {
    echo -e "${BLUE}=== 服务状态检查 ===${NC}"
    
    for service_info in "${SERVICES[@]}"; do
        IFS=':' read -r service_name port description <<< "$service_info"
        
        printf "%-15s %-6s %-20s " "$service_name" "$port" "$description"
        
        if check_port "$port"; then
            log_success "运行中"
            
            # 对于nginx，额外检查HTTP响应
            if [[ "$service_name" == "nginx" ]]; then
                printf "  HTTP检查: "
                if http_code=$(check_http "http://localhost"); then
                    log_success "响应正常 ($http_code)"
                else
                    log_error "响应异常 ($http_code)"
                fi
            fi
        else
            log_error "未运行"
        fi
    done
    
    echo ""
}

# 检查网站访问
check_website() {
    echo -e "${BLUE}=== 网站访问检查 ===${NC}"
    
    local urls=(
        "http://$DOMAIN:首页"
        "https://$DOMAIN:HTTPS首页"
        "http://$DOMAIN/manager:运营平台"
        "http://$DOMAIN/agent:代理商平台"
        "http://$DOMAIN/merchant:商户平台"
        "http://$DOMAIN/health:健康检查"
    )
    
    for url_info in "${urls[@]}"; do
        IFS=':' read -r url description <<< "$url_info"
        
        printf "%-35s " "$description"
        
        if http_code=$(check_http "$url" 5); then
            log_success "正常 ($http_code)"
        else
            log_error "异常 ($http_code)"
        fi
    done
    
    echo ""
}

# 检查日志错误
check_logs() {
    echo -e "${BLUE}=== 最近错误日志 ===${NC}"
    
    local log_files=(
        "/var/log/nginx/unipay-portal.error.log:Nginx错误日志"
        "/var/log/nginx/error.log:Nginx系统错误日志"
    )
    
    for log_info in "${log_files[@]}"; do
        IFS=':' read -r log_file description <<< "$log_info"
        
        if [[ -f "$log_file" ]]; then
            echo -e "${YELLOW}$description:${NC}"
            tail -n 5 "$log_file" 2>/dev/null | sed 's/^/  /' || echo "  无法读取日志文件"
            echo ""
        fi
    done
}

# 检查SSL证书状态
check_ssl() {
    echo -e "${BLUE}=== SSL证书状态 ===${NC}"
    
    if [[ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]]; then
        local cert_info=$(openssl x509 -in "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" -noout -dates 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            echo -e "${GREEN}证书存在${NC}"
            echo "$cert_info" | sed 's/^/  /'
            
            # 检查证书过期时间
            local expire_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
            local expire_timestamp=$(date -d "$expire_date" +%s 2>/dev/null)
            local current_timestamp=$(date +%s)
            local days_left=$(( (expire_timestamp - current_timestamp) / 86400 ))
            
            if [[ $days_left -gt 30 ]]; then
                log_success "证书有效，还有 $days_left 天过期"
            elif [[ $days_left -gt 7 ]]; then
                log_warning "证书即将过期，还有 $days_left 天"
            else
                log_error "证书即将过期，还有 $days_left 天！"
            fi
        else
            log_error "证书文件损坏"
        fi
    else
        log_warning "未找到SSL证书"
    fi
    
    echo ""
}

# 生成监控报告
generate_report() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local report_file="/tmp/unipay-monitor-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "UniPay Portal 监控报告"
        echo "生成时间: $timestamp"
        echo "========================================"
        echo ""
        
        check_system_resources
        check_services
        check_website
        check_ssl
        check_logs
        
    } | tee "$report_file"
    
    echo -e "${BLUE}监控报告已保存到: $report_file${NC}"
}

# 实时监控模式
real_time_monitor() {
    echo -e "${BLUE}启动实时监控模式 (按Ctrl+C退出)${NC}"
    echo ""
    
    while true; do
        clear
        echo -e "${BLUE}UniPay Portal 实时监控 - $(date '+%Y-%m-%d %H:%M:%S')${NC}"
        echo "========================================"
        echo ""
        
        check_system_resources
        check_services
        check_website
        
        echo -e "${YELLOW}5秒后刷新...${NC}"
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo "UniPay Portal 监控脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -r, --report   生成完整监控报告"
    echo "  -w, --watch    实时监控模式"
    echo "  -s, --services 仅检查服务状态"
    echo "  -l, --logs     仅检查错误日志"
    echo "  --ssl          仅检查SSL证书状态"
    echo ""
    echo "示例:"
    echo "  $0              # 运行完整检查"
    echo "  $0 -r           # 生成报告文件"
    echo "  $0 -w           # 实时监控"
    echo "  $0 -s           # 仅检查服务"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -r|--report)
            generate_report
            ;;
        -w|--watch)
            real_time_monitor
            ;;
        -s|--services)
            check_services
            ;;
        -l|--logs)
            check_logs
            ;;
        --ssl)
            check_ssl
            ;;
        "")
            echo -e "${BLUE}UniPay Portal 监控检查 - $(date '+%Y-%m-%d %H:%M:%S')${NC}"
            echo "========================================"
            echo ""
            
            check_system_resources
            check_services
            check_website
            check_ssl
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 $0 --help 查看帮助信息"
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi