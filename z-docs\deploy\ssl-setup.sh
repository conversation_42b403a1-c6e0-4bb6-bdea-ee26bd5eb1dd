#!/bin/bash

# UniPay Portal SSL证书自动配置脚本
# 使用Let's Encrypt免费证书
# 版本: v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DOMAIN="ybdl.shop"
EMAIL="admin@${DOMAIN}"  # 请修改为您的邮箱
NGINX_CONF_PATH="/etc/nginx/sites-available/unipay-portal"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 安装certbot
install_certbot() {
    log_info "安装Certbot..."
    
    if [[ -f /etc/redhat-release ]]; then
        # CentOS/RHEL
        yum install -y epel-release
        yum install -y certbot python3-certbot-nginx
    else
        # Debian/Ubuntu
        apt-get update
        apt-get install -y certbot python3-certbot-nginx
    fi
    
    log_success "Certbot安装完成"
}

# 获取SSL证书
obtain_certificate() {
    log_info "获取SSL证书..."
    
    # 停止nginx以释放80端口
    systemctl stop nginx
    
    # 获取证书
    certbot certonly --standalone \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        -d "$DOMAIN" \
        -d "www.$DOMAIN"
    
    log_success "SSL证书获取成功"
}

# 更新nginx配置
update_nginx_config() {
    log_info "更新Nginx配置以启用SSL..."
    
    # 备份当前配置
    cp "$NGINX_CONF_PATH" "${NGINX_CONF_PATH}.bak"
    
    # 创建新的SSL配置
    cat > "$NGINX_CONF_PATH" << 'EOF'
# UniPay 门户网站 Nginx 配置 (SSL版本)

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name ybdl.shop www.ybdl.shop;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name ybdl.shop www.ybdl.shop;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/ybdl.shop/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ybdl.shop/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 网站根目录 - 门户首页
    location / {
        root /www/wwwroot/unipay-portal;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 添加安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    }
    
    # 静态资源缓存
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /www/wwwroot/unipay-portal;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }
    
    # 运营平台代理 (端口 9217)
    location /manager {
        proxy_pass http://127.0.0.1:9217;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 代理商平台代理 (端口 9219)
    location /agent {
        proxy_pass http://127.0.0.1:9219;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 商户平台代理 (端口 9218)
    location /merchant {
        proxy_pass http://127.0.0.1:9218;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 支付网关代理 (端口 9216) - 可选
    location /pay {
        proxy_pass http://127.0.0.1:9216;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API 接口代理
    location /api/ {
        proxy_pass http://127.0.0.1:9217;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 支持
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS, PUT, DELETE';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /www/wwwroot/unipay-portal;
        internal;
    }
    
    location = /50x.html {
        root /www/wwwroot/unipay-portal;
        internal;
    }
    
    # 日志配置
    access_log /var/log/nginx/unipay-portal.access.log;
    error_log /var/log/nginx/unipay-portal.error.log;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
EOF
    
    log_success "Nginx配置更新完成"
}

# 设置证书自动续期
setup_auto_renewal() {
    log_info "设置证书自动续期..."
    
    # 添加crontab任务
    (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'systemctl reload nginx'") | crontab -
    
    log_success "证书自动续期设置完成"
}

# 测试配置并重启nginx
restart_nginx() {
    log_info "测试配置并重启Nginx..."
    
    # 测试配置
    if nginx -t; then
        systemctl start nginx
        systemctl reload nginx
        log_success "Nginx重启成功"
    else
        log_error "Nginx配置测试失败"
        # 恢复备份配置
        cp "${NGINX_CONF_PATH}.bak" "$NGINX_CONF_PATH"
        systemctl start nginx
        exit 1
    fi
}

# 验证SSL配置
verify_ssl() {
    log_info "验证SSL配置..."
    
    sleep 5  # 等待nginx完全启动
    
    # 检查HTTPS访问
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN" | grep -q "200"; then
        log_success "HTTPS访问正常"
    else
        log_warning "HTTPS访问可能存在问题"
    fi
    
    # 检查证书信息
    echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "         UniPay Portal SSL证书配置脚本 v1.0"
    echo "=================================================="
    echo -e "${NC}"
    
    echo -e "${YELLOW}注意: 请确保域名 $DOMAIN 已正确解析到此服务器${NC}"
    echo -e "${YELLOW}邮箱地址: $EMAIL (用于证书通知)${NC}"
    echo ""
    
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    check_root
    install_certbot
    obtain_certificate
    update_nginx_config
    setup_auto_renewal
    restart_nginx
    verify_ssl
    
    echo ""
    log_success "SSL证书配置完成！"
    echo -e "${GREEN}网站现在可以通过HTTPS访问:${NC}"
    echo -e "  https://$DOMAIN"
    echo -e "  https://www.$DOMAIN"
    echo ""
    echo -e "${BLUE}证书信息:${NC}"
    echo -e "  证书路径: /etc/letsencrypt/live/$DOMAIN/"
    echo -e "  自动续期: 已设置 (每天12:00检查)"
    echo -e "  有效期: 90天"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi