# 应用配置重置

<cite>
**本文档引用的文件**   
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [配置重置消息机制](#配置重置消息机制)
4. [消息接收与处理流程](#消息接收与处理流程)
5. [业务流程分析](#业务流程分析)
6. [线程安全与性能优化](#线程安全与性能优化)
7. [系统集成与应用场景](#系统集成与应用场景)
8. [结论](#结论)

## 简介
本文档详细阐述了统一支付系统中的应用配置重置机制。该机制通过消息队列实现跨系统的全局配置同步，确保所有子系统能够及时响应配置变更。文档重点分析了ResetAppConfigMQ消息的触发条件、传输机制和系统影响，以及ResetAppConfigMQReceiver如何接收并处理配置重置通知。同时，文档还深入探讨了配置重置的完整业务流程、线程安全考虑和性能优化措施，展示了该机制如何支持系统的动态配置更新能力。

## 核心组件

本文档涉及的核心组件包括配置重置消息模型、消息接收器、系统配置服务和应用配置数据模型。这些组件协同工作，实现了跨系统的配置同步功能。

**Section sources**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L92)
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java#L16-L84)

## 配置重置消息机制

### 消息模型定义
ResetAppConfigMQ类定义了配置重置消息的数据结构和行为规范。该消息采用广播模式发送，确保所有订阅系统都能接收到配置更新通知。

```mermaid
classDiagram
class ResetAppConfigMQ {
+String MQ_NAME
+MsgPayload payload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(groupKey) ResetAppConfigMQ
+parse(msg) MsgPayload
}
class MsgPayload {
+String groupKey
}
ResetAppConfigMQ --> MsgPayload : "包含"
```

**Diagram sources **
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)

### 消息触发条件
配置重置消息在以下情况下被触发：
- 系统管理员通过管理后台修改全局配置
- 配置服务检测到配置版本变更
- 手动执行配置刷新命令
- 系统启动时的初始化配置加载

消息的groupKey字段指定了需要重置的配置组，允许系统有针对性地更新特定配置集。

**Section sources**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)

## 消息接收与处理流程

### 消息接收器实现
系统中的多个子系统（包括管理平台、商户系统、支付网关和代理商系统）都实现了ResetAppConfigMQReceiver，用于接收和处理配置重置消息。

```mermaid
sequenceDiagram
participant MQBroker as "消息代理"
participant Manager as "管理平台"
participant Merchant as "商户系统"
participant Payment as "支付网关"
participant Agent as "代理商系统"
MQBroker->>Manager : 广播ResetAppConfigMQ
MQBroker->>Merchant : 广播ResetAppConfigMQ
MQBroker->>Payment : 广播ResetAppConfigMQ
MQBroker->>Agent : 广播ResetAppConfigMQ
Manager->>Manager : receive()处理消息
Merchant->>Merchant : receive()处理消息
Payment->>Payment : receive()处理消息
Agent->>Agent : receive()处理消息
```

**Diagram sources **
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java#L15-L29)

### 消息处理逻辑
所有子系统的消息接收器都遵循相同的处理逻辑：接收消息后，调用SysConfigService的initDBConfig方法，根据消息中的groupKey参数重新加载指定的配置组。

**Section sources**
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java#L15-L29)

## 业务流程分析

### 配置重置完整流程
配置重置的完整业务流程包括消息发布、接收、缓存清除和重新加载四个主要阶段。

```mermaid
flowchart TD
A[配置变更] --> B[发布ResetAppConfigMQ]
B --> C{消息广播}
C --> D[管理平台接收]
C --> E[商户系统接收]
C --> F[支付网关接收]
C --> G[代理商系统接收]
D --> H[清除本地缓存]
E --> I[清除本地缓存]
F --> J[清除本地缓存]
G --> K[清除本地缓存]
H --> L[重新加载最新配置]
I --> M[重新加载最新配置]
J --> N[重新加载最新配置]
K --> O[重新加载最新配置]
L --> P[配置更新完成]
M --> P
N --> P
O --> P
```

**Diagram sources **
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

### 缓存管理策略
系统采用内存缓存策略来提高配置访问性能。当配置重置消息到达时，系统会清除相应的缓存条目，并从数据库重新加载最新配置。

**Section sources**
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

## 线程安全与性能优化

### 线程安全实现
SysConfigService的initDBConfig方法使用synchronized关键字确保线程安全，防止多个线程同时修改配置缓存导致数据不一致。

```mermaid
classDiagram
class SysConfigService {
+static boolean IS_USE_CACHE
+static MutablePair<String, DBApplicationConfig> APPLICATION_CONFIG
+synchronized initDBConfig(groupKey) void
+getDBApplicationConfig() DBApplicationConfig
+selectByGroupKey(groupKey) JSONObject
}
class DBApplicationConfig {
+String mgrSiteUrl
+String mchSiteUrl
+String agentSiteUrl
+String paySiteUrl
+String ossPublicSiteUrl
}
SysConfigService --> DBApplicationConfig : "持有"
```

**Diagram sources **
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L92)
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java#L16-L84)

### 性能优化措施
系统实现了多项性能优化措施：
- 配置缓存：减少数据库查询次数，提高配置访问速度
- 懒加载：仅在需要时才从数据库加载配置
- 条件检查：在缓存未启用时直接返回，避免不必要的处理
- 批量操作：支持批量更新配置项，减少数据库交互次数

**Section sources**
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

## 系统集成与应用场景

### 跨系统配置同步
该机制支持多个子系统之间的配置同步，确保所有系统使用一致的配置信息。

```mermaid
graph TB
subgraph "配置源"
ConfigSource[配置管理后台]
end
subgraph "消息系统"
MQ[消息队列]
end
subgraph "应用系统"
Manager[管理平台]
Merchant[商户系统]
Payment[支付网关]
Agent[代理商系统]
end
ConfigSource --> MQ
MQ --> Manager
MQ --> Merchant
MQ --> Payment
MQ --> Agent
```

**Diagram sources **
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)

### 实际应用场景
该机制在以下场景中发挥重要作用：
- 全局支付参数调整：如费率、限额等配置变更
- 系统地址更新：管理平台、商户系统等URL变更
- 支付渠道配置：新增或修改支付接口参数
- 安全策略更新：如加密密钥、认证方式等变更
- 运营活动配置：促销活动、优惠券等营销配置

**Section sources**
- [DBApplicationConfig.java](file://core/src/main/java/com/unipay/core/model/DBApplicationConfig.java#L16-L84)

## 结论
应用配置重置机制通过消息队列实现了跨系统的高效配置同步。该机制具有以下优势：
- **实时性**：配置变更能够立即通知所有相关系统
- **一致性**：确保所有子系统使用相同的配置版本
- **可靠性**：基于消息队列的广播模式保证消息不丢失
- **可扩展性**：易于添加新的子系统作为消息订阅者
- **性能优越**：通过缓存机制减少数据库压力

该机制为统一支付系统提供了强大的动态配置管理能力，支持系统在不停机的情况下进行配置更新，提高了系统的灵活性和可维护性。