
# 支付网关

<cite>
**本文档引用的文件**   
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java)
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java)
- [RefundOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java)
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java)
- [ConfigContextService.java](file://sys-payment\src\main\java\com\unipay\pay\service\ConfigContextService.java)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java)
- [PaywayUtil.java](file://sys-payment\src\main\java\com\unipay\pay\util\PaywayUtil.java)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java)
</cite>

## 目录
1. [支付网关核心职责](#支付网关核心职责)
2. [核心支付功能实现](#核心支付功能实现)
   - [统一下单](#统一下单)
   - [支付查询](#支付查询)
   - [退款](#退款)
   - [转账](#转账)
3. [支付渠道集成与路由](#支付渠道集成与路由)
4. [核心模块依赖关系](#核心模块依赖关系)
5. [支付请求处理流程](#支付请求处理流程)
6. [异常处理机制](#异常处理机制)
7. [性能优化建议](#性能优化建议)

## 支付网关核心职责

支付网关是整个支付系统的核心组件，负责处理所有与支付相关的业务逻辑。其主要职责包括：
- **统一下单**：接收商户的支付请求，生成统一的支付订单，并调用相应的支付渠道完成支付。
- **支付查询**：提供支付订单状态查询功能，确保商户能够实时获取支付结果。
- **退款**：支持商户发起退款请求，处理退款订单并通知商户退款结果。
- **转账**：支持商户发起转账请求，处理转账订单并通知商户转账结果。
- **分账**：支持订单分账功能，自动将资金分配给不同的收款方。
- **通知**：在支付、退款、转账等操作完成后，向商户发送异步通知。

**Section sources**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java#L12-L30)
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)

## 核心支付功能实现

### 统一下单

统一下单功能是支付网关的核心功能之一，负责接收商户的支付请求并生成统一的支付订单。具体实现如下：

1. **参数校验**：首先对商户传入的支付请求参数进行校验，确保所有必要参数都已提供且符合要求。
2. **订单生成**：根据校验通过的参数生成支付订单，包括订单号、支付金额、支付方式等信息。
3. **支付渠道选择**：根据支付方式选择合适的支付渠道，如微信支付、支付宝等。
4. **调用支付接口**：调用选定支付渠道的支付接口，发起支付请求。
5. **结果处理**：处理支付渠道返回的结果，更新订单状态，并向商户返回支付结果。

```mermaid
sequenceDiagram
participant 商户 as 商户
participant 支付网关 as 支付网关
participant 支付渠道 as 支付渠道
商户->>支付网关 : 发起支付请求
支付网关->>支付网关 : 参数校验
支付网关->>支付网关 : 生成支付订单
支付网关->>支付网关 : 选择支付渠道
支付网关->>支付渠道 : 调用支付接口
支付渠道-->>支付网关 : 返回支付结果
支付网关->>支付网关 : 更新订单状态
支付网关-->>商户 : 返回支付结果
```

**Diagram sources**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java#L12-L30)
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)

**Section sources**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java#L12-L30)
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)

### 支付查询

支付查询功能允许商户查询支付订单的状态，确保支付结果的准确性。具体实现如下：

1. **订单查询**：根据商户提供的订单号或商户订单号查询支付订单。
2. **状态更新**：如果订单状态为“支付中”，则调用支付渠道的查询接口获取最新的支付状态。
3. **结果返回**：将查询到的订单状态返回给商户。

```mermaid
sequenceDiagram
participant 商户 as 商户
participant 支付网关 as 支付网关
participant 支付渠道 as 支付渠道
商户->>支付网关 : 发起支付查询请求
支付网关->>支付网关 : 查询订单状态
alt 订单状态为支付中
支付网关->>支付渠道 : 调用查询接口
支付渠道-->>支付网关 : 返回最新状态
支付网关->>支付网关 : 更新订单状态
end
支付网关-->>商户 : 返回订单状态
```

**Diagram sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L80-L95)

**Section sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L80-L95)

### 退款

退款功能允许商户发起退款请求，处理退款订单并通知商户退款结果。具体实现如下：

1. **参数校验**：对商户传入的退款请求参数进行校验，确保所有必要参数都已提供且符合要求。
2. **订单生成**：生成退款订单，包括退款金额、退款原因等信息。
3. **调用退款接口**：调用支付渠道的退款接口，发起退款请求。
4. **结果处理**：处理支付渠道返回的结果，更新退款订单状态，并向商户发送退款结果通知。

```mermaid
sequenceDiagram
participant 商户 as 商户
participant 支付网关 as 支付网关
participant 支付渠道 as 支付渠道
商户->>支付网关 : 发起退款请求
支付网关->>支付网关 : 参数校验
支付网关->>支付网关 : 生成退款订单
支付网关->>支付渠道 : 调用退款接口
支付渠道-->>支付网关 : 返回退款结果
支付网关->>支付网关 : 更新退款订单状态
支付网关->>商户 : 发送退款结果通知
```

**Diagram sources**
- [RefundOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java#L15-L47)

**Section sources**
- [RefundOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java#L15-L47)

### 转账

转账功能允许商户发起转账请求，处理转账订单并通知商户转账结果。具体实现如下：

1. **参数校验**：对商户传入的转账请求参数进行校验，确保所有必要参数都已提供且符合要求。
2. **订单生成**：生成转账订单，包括转账金额、收款人信息等。
3. **调用转账接口**：调用支付渠道的转账接口，发起转账请求。
4. **结果处理**：处理支付渠道返回的结果，更新转账订单状态，并向商户发送转账结果通知。

```mermaid
sequenceDiagram
participant 商户 as 商户
participant 支付网关 as 支付网关
participant 支付渠道 as 支付渠道
商户->>支付网关 : 发起转账请求
支付网关->>支付网关 : 参数校验
支付网关->>支付网关 : 生成转账订单
支付网关->>支付渠道 : 调用转账接口
支付渠道-->>支付网关 : 返回转账结果
支付网关->>支付网关 : 更新转账订单状态
支付网关->>商户 : 发送转账结果通知
```

**Diagram sources**
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java#L19-L141)

**Section sources**
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java#L19-L141)

## 支付渠道集成与路由

支付网关通过`IPaymentService`接口与不同的支付渠道（如微信、支付宝）进行集成。每个支付渠道都有一个具体的实现类，这些实现类继承自`AbstractPaymentService`抽象类，并实现了`IPaymentService`接口。

### IPaymentService接口

`IPaymentService`接口定义了支付服务的基本方法，包括获取接口代码、检查支付方式支持、前置检查、自定义支付订单号和调用支付接口等。

```java
public interface IPaymentService {

    /** 获取到接口code **/
    String getIfCode();

    /** 是否支持该支付方式 */
    boolean isSupport(String wayCode);

    /** 前置检查如参数等信息是否符合要求， 返回错误信息或直接抛出异常即可  */
    String preCheck(UnifiedOrderRQ bizRQ, PayOrder payOrder);

    /** 自定义支付订单号， 若返回空则使用系统生成订单号 */
    String customPayOrderId(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext);

    /** 调起支付接口，并响应数据；  内部处理普通商户和服务商模式  **/
    AbstractRS pay(UnifiedOrderRQ bizRQ, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception;
}
```

### 策略模式动态路由

支付网关使用策略模式动态路由到具体的支付实现。当商户发起支付请求时，支付网关根据支付方式选择合适的支付渠道，并调用相应的支付服务。

```mermaid
classDiagram
class IPaymentService {
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, mchAppConfigContext) String
+pay(bizRQ, payOrder, mchAppConfigContext) AbstractRS
}
class AbstractPaymentService {
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, mchAppConfigContext) String
+pay(bizRQ, payOrder, mchAppConfigContext) AbstractRS
}
class WxPaymentService {
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, mchAppConfigContext) String
+pay(bizRQ, payOrder, mchAppConfigContext) AbstractRS
}
class AlipayPaymentService {
+getIfCode() String
+isSupport(wayCode) boolean
+preCheck(bizRQ, payOrder) String
+customPayOrderId(bizRQ, payOrder, mchAppConfigContext) String
+pay(bizRQ, payOrder, mchAppConfigContext) AbstractRS
}
IPaymentService <|-- AbstractPaymentService
AbstractPaymentService <|-- WxPaymentService
AbstractPaymentService <|-- AlipayPaymentService
```

**Diagram sources**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java#L12-L30)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java#L17-L57)

**Section sources**
- [IPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\IPaymentService.java#L12-L30)
- [AbstractPaymentService.java](file://sys-payment\src\main\java\com\unipay\pay\channel\AbstractPaymentService.java#L17-L57)

## 核心模块依赖关系

支付网关与核心模块（core）和消息队列组件（components-mq）有紧密的依赖关系。

### 与核心模块（core）的依赖

支付网关依赖核心模块中的多个服务，包括`PayOrderService`、`RefundOrderService`、`TransferOrderService`等，用于处理支付订单、退款订单和转账订单的创建、更新和查询。

```mermaid
classDiagram
class PayOrderProcessService {
+confirmSuccess(payOrder) void
+updatePayOrderAutoDivision(payOrder) void
+updateIngAndSuccessOrFailByCreatebyOrder(payOrder, channelRetMsg) void
}
class PayOrderService {
+updateInit2Ing(payOrderId, payOrder) boolean
+updateIng2SuccessOrFail(payOrderId, state, channelOrderId, channelUserId, channelErrCode, channelErrMsg) boolean
}
class RefundOrderProcessService {
+handleRefundOrder4Channel(channelRetMsg, refundOrder) boolean
}
class RefundOrderService {
+updateInit2Ing(refundOrderId, channelOrderNo) boolean
+updateIng2Success(refundOrderId, channelOrderNo) boolean
+updateIng2Fail(refundOrderId, channelOrderNo, channelErrCode, channelErrMsg) boolean
}
class TransferOrderReissueService {
+processOrder(transferOrder) ChannelRetMsg
+processChannelMsg(channelRetMsg, transferOrder) void
}
class TransferOrderService {
+updateInit2Ing(transferId, channelResData) boolean
+updateIng2Success(transferId, channelOrderNo) boolean
+updateIng2Fail(transferId, channelOrderNo, channelErrCode, channelErrMsg) boolean
}
PayOrderProcessService --> PayOrderService : 依赖
RefundOrderProcessService --> RefundOrderService : 依赖
TransferOrderReissueService --> TransferOrderService : 依赖
```

**Diagram sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)
- [RefundOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java#L15-L47)
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java#L19-L141)

**Section sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L20-L97)
- [RefundOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java#L15-L47)
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java#L19-L141)

### 与消息队列组件（components-mq）的依赖

支付网关依赖消息队列组件（components-mq）进行异步任务处理，如分账任务、补单任务等。通过消息队列，支付网关可以将耗时的任务异步处理，提高系统的响应速度和稳定性。

```mermaid
classDiagram
class PayOrderProcessService {
+mqSender IMQSender
}
class TransferOrderReissueService {
+transferOrderReissueService TransferOrderReissueService
}
class IMQSender {
+send(mqModel) void
+send(mqModel, delay) void
}
PayOrderProcessService --> IMQSender : 依赖
TransferOrderReissueService --> IMQSender : 依赖
```

**Diagram sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L27-L27)
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com\unipay\pay\service\TransferOrderReissueService.java#L23-L23)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)

**Section sources**
- [PayOrderProcessService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayOrderProcessService.java#L27-L27)
- [TransferOrderReissueService.java](file://sys-payment\src\main\java\com