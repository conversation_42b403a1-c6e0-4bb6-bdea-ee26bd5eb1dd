
# API接口文档

<cite>
**本文档引用的文件**   
- [api1.md](file://sys-payment/src/main/resources/markdown/doc/api1.md)
- [api2.md](file://sys-payment/src/main/resources/markdown/doc/api2.md)
- [api3.md](file://sys-payment/src/main/resources/markdown/doc/api3.md)
- [api4.md](file://sys-payment/src/main/resources/markdown/doc/api4.md)
- [api5.md](file://sys-payment/src/main/resources/markdown/doc/api5.md)
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java)
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java)
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java)
- [QueryRefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/QueryRefundOrderController.java)
- [TransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/TransferOrderController.java)
- [QueryTransferOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/transfer/QueryTransferOrderController.java)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java)
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java)
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java)
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java)
- [QueryRefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/QueryRefundOrderRQ.java)
- [TransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRQ.java)
- [QueryTransferOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRQ.java)
- [UnifiedOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRS.java)
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java)
- [ClosePayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRS.java)
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java)
- [QueryRefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/QueryRefundOrderRS.java)
- [TransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/TransferOrderRS.java)
- [QueryTransferOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/transfer/QueryTransferOrderRS.java)
- [ApiController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/ApiController.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [TransferOrder.java](file://core/src/main/java/com/unipay/core/entity/TransferOrder.java)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)
- [ApiCodeEnum.java](file://core/src/main/java/com/unipay/core/constants/ApiCodeEnum.java)
</cite>

## 目录
1. [接口通用规则](#接口通用规则)
2. [核心支付API](#核心支付api)
3. [退款API](#退款api)
4. [转账API](#转账api)
5. [分账API](#分账api)
6. [错误码体系](#错误码体系)
7. [安全与签名机制](#安全与签名机制)
8. [客户端SDK与最佳实践](#客户端sdk与最佳实践)

## 接口通用规则

### 协议规则
- **传输方式**：采用HTTP传输（生产环境建议使用HTTPS）
- **提交方式**：`POST` 或 `GET`
- **内容类型**：`application/json`
- **字符编码**：`UTF-8`
- **签名算法**：`MD5`

### 参数规范
- **交易金额**：默认为人民币交易，单位为分，参数值不能带小数。
- **时间参数**：所有涉及时间参数均使用精确到毫秒的13位数值，如：1622016572190。时间戳指从格林尼治时间1970年01月01日00时00分00秒起至现在的毫秒数。

**Section sources**
- [api1.md](file://sys-payment/src/main/resources/markdown/doc/api1.md#L1-L10)

## 核心支付API

### 统一下单
商户业务系统通过统一下单接口发起支付收款订单，支付网关会根据商户配置的支付通道路由完成支付下单。

**接口说明**
- **适用对象**：普通商户、特约商户
- **请求URL**：https://pay.jeepay.vip/api/pay/unifiedOrder
- **请求方式**：`POST`
- **请求类型**：`application/json` 或 `application/x-www-form-urlencoded`

**请求参数**

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 商户号 | mchNo | 是 | String(30) | M1621873433953 | 商户号 |
| 应用ID | appId | 是 | String(24) | 60cc09bce4b0f1c0b83761c9 | 应用ID |
| 商户订单号 | mchOrderNo | 是 | String(30) | 20160427210604000490 | 商户生成的订单号 |
| 支付方式 | wayCode | 是 | String(30) | WX_LITE | 支付方式,如微信小程序WX_LITE |
| 支付金额 | amount | 是 | int | 100 | 支付金额,单位分 |
| 货币代码 | currency | 是 | String(3) | cny | 三位货币代码,人民币:cny |
| 客户端IP | clientIp | 否 | String(32) | ************* | 客户端IPV4地址 |
| 商品标题 | subject | 是 | String(64) | Jeepay商品标题测试 | 商品标题 |
| 商品描述 | body | 是 | String(256) | Jeepay商品描述测试 | 商品描述 |
| 异步通知地址 | notifyUrl | 否 | String(128) | https://127.0.0.1:8080/notify.htm | 支付结果异步回调URL |
| 跳转通知地址 | returnUrl | 否 | String(128) | https://127.0.0.1:8080/return.htm | 支付结果同步跳转通知URL |
| 失效时间 | expiredTime | 否 | int | 3600 | 订单失效时间,单位秒,默认2小时 |
| 渠道参数 | channelExtra | 否 | String(256) | {"auth_code", "13920933111042"} | 特定渠道发起的额外参数,json格式字符串 |
| 分账模式 | divisionMode | 否 | int | 0 | 分账模式： 0-该笔订单不允许分账[默认], 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额) |
| 扩展参数 | extParam | 否 | String(512) | 134586944573118714 | 商户扩展参数,回调时会原样返回 |
| 请求时间 | reqTime | 是 | long | 1622016572190 | 请求接口时间,13位时间戳 |
| 接口版本 | version | 是 | String(3) | 1.0 | 接口版本号，固定：1.0 |
| 签名 | sign | 是 | String(32) | C380BEC2BFD727A4B6845133519F3AD6 | 签名值，详见签名算法 |
| 签名类型 | signType | 是 | String(32) | MD5 | 签名类型，目前只支持MD5方式 |

**返回参数**

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 返回状态 | code | 是 | int | 0 | 0-处理成功，其他-处理有误，详见错误码 |
| 返回信息 | msg | 否 | String(128) | 签名失败 | 具体错误原因 |
| 签名信息 | sign | 否 | String(32) | CCD9083A6DAD9A2DA9F668C3D4517A84 | 对data内数据签名 |
| 返回数据 | data | 否 | String(512) | {} | 返回下单数据,json格式数据 |

**data数据格式**

| 字段名 | 变量名 | 必填 | 类型 | 示例值 | 描述 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 支付订单号 | payOrderId | 是 | String(30) | U12021022311124442600 | 返回支付系统订单号 |
| 商户订单号 | mchOrderNo | 是 | String(30) | 20160427210604000490 | 返回商户传入的订单号 |
| 订单状态 | orderState | 是 | int | 2 | 支付订单状态<br>0-订单生成<br>1-支付中<br>2-支付成功<br>3-支付失败<br>4-已撤销<br>5-已退款<br>6-订单关闭 |
| 支付数据类型 | payDataType | 是 | String | payUrl | 支付参数类型<br>payUrl-跳转链接的方式<br>form-表单方式<br>wxapp-微信支付参数<br>aliapp-支付宝app支付参数<br>ysfapp-云闪付app支付参数<br>codeUrl-二维码地址<br>codeImgUrl-二维码图片地址<br>none-空支付参数 |
| 支付数据 | payData | 否 | String | http://www.unipay.com/pay.html | 发起支付用到的支付参数 |
| 渠道错误码 | errCode | 否 | String | ACQ.PAYMENT_AUTH_CODE_INVALID | 上游渠道返回的错误码 |
| 渠道错误描述 | errMsg | 否 | String | Business Failed 失败 | 上游渠道返回的错误描述 |

**请求示例数据**
```json
{
  "amount": 8,
  "extParam": "",
  "mchOrderNo": "mho1624005107281",
  "subject": "商品标题",
  "wayCode": "ALI_BAR",
  "sign": "84F606FA25A6EC4783BECC08D4FDC681",
  "reqTime": "1624005107",
  "body": "商品描述",
  "version": "1.0",
  "channelExtra": "{\"authCode\":\"280812820366966512\"}",
  "appId": "60cc09bce4b0f1c0b83761c9",
  "clientIp": "*************",
  "notifyUrl": "https://127.0.0.1:8080",
  "signType": "MD5",
  "currency": "cny",
  "returnUrl": "",
  "mchNo": "M1623984572",
  "divisionMode": 1
}
```

**返回示例数据**
```json
{
  "code": 0,
  "data": {
    "errCode": "ACQ.PAYMENT_AUTH_CODE_INVALID",
    "errMsg": "Business Failed【支付失败，获取顾客账户信息失败，请顾客刷新付款码后重新收款，如再次收款失败，请联系管理员处理。[SOUNDWAVE_PARSER_FAIL]】",
    "mchOrderNo": "mho1624005752661",
    "orderState": 3,
    "payOrderId": "P202106181642329900002"
  },
  "msg": "SUCCESS",
  "sign": "F4DA202C516D1F33A12F1E547C5004FD"
}
```

**Section sources**
- [api2.md](file://sys-payment/src/main/resources/markdown/doc/api2.md#L1-L150)
- [UnifiedOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/UnifiedOrderController.java#L24-L91)
- [UnifiedOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/UnifiedOrderRQ.java#L23-L159)
-