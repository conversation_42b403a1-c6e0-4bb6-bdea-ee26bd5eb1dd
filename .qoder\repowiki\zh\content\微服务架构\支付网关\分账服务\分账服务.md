
# 分账服务

<cite>
**本文档引用的文件**  
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [IDivisionService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IDivisionService.java)
- [DivisionRecordChannelNotifyController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/division/DivisionRecordChannelNotifyController.java)
- [PayOrderDivisionRecordReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/PayOrderDivisionRecordReissueTask.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
</cite>

## 目录
1. [分账服务概述](#分账服务概述)
2. [分账模式实现机制](#分账模式实现机制)
3. [分账请求生成与处理](#分账请求生成与处理)
4. [分账结果异步通知处理](#分账结果异步通知处理)
5. [分账失败重试机制](#分账失败重试机制)
6. [消息队列解耦与最终一致性](#消息队列解耦与最终一致性)
7. [核心数据模型](#核心数据模型)

## 分账服务概述

分账服务是支付系统中的关键组件，负责将支付成功后的资金按照预设规则分配给多个接收方。系统支持自动分账和手动分账两种模式，通过消息队列实现支付成功与分账执行的解耦，确保分账操作的最终一致性。分账流程涉及分账规则解析、分账请求生成、调用支付渠道分账接口、处理异步通知和失败重试等多个环节。

**Section sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L35-L298)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L1-L279)

## 分账模式实现机制

系统支持三种分账模式：禁止分账、自动分账和手动分账。自动分账在支付成功后由系统自动触发，手动分账则由商户主动发起。分账模式由支付订单的`divisionMode`字段控制，分账状态由`divisionState`字段跟踪。当支付成功且分账模式为自动分账时，系统会将分账任务推送到消息队列，由消费者异步执行分账操作。

```mermaid
flowchart TD
    Start([支付成功]) --> CheckMode["检查分账模式"]
    CheckMode -->|自动分账| SendToMQ["发送分账MQ消息"]
    CheckMode -->|手动分账| WaitManual["等待商户手动触发"]
    SendToMQ --> MQ["消息队列"]
    WaitManual --> ManualTrigger["商户调用分账接口"]
    ManualTrigger --> Process["执行分账处理"]
    MQ --> Process
    Process --> CallChannel["调用支付渠道分账接口"]
    CallChannel --> CheckResult["检查分账结果"]
    CheckResult -->|成功| UpdateSuccess["更新分账记录为成功"]
    CheckResult -->|失败| UpdateFail["更新分账记录为失败"]
    CheckResult -->|受理中| WaitNotify