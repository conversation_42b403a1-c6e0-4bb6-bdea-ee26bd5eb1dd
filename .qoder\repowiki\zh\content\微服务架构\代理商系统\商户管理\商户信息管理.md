# 商户信息管理

<cite>
**本文档引用文件**   
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [SysUserAuthService.java](file://service/src/main/java/com/unipay/service/impl/SysUserAuthService.java)
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)
- [IsvInfo.java](file://core/src/main/java/com/unipay/core/entity/IsvInfo.java)
</cite>

## 目录
1. [商户信息实体类](#商户信息实体类)
2. [商户信息管理API接口](#商户信息管理api接口)
3. [代理商商户生命周期管理](#代理商商户生命周期管理)
4. [数据校验与异常处理](#数据校验与异常处理)
5. [服务层业务逻辑](#服务层业务逻辑)
6. [批量导入与性能优化](#批量导入与性能优化)

## 商户信息实体类

`MchInfo`实体类定义了商户的核心信息，包含商户基本信息、联系人信息和状态信息等字段。该类通过`@TableName("t_mch_info")`注解映射到数据库表`t_mch_info`。

### 商户基本信息字段

| 字段名 | 类型 | 业务含义 |
| :--- | :--- | :--- |
| **mchNo** | String | 商户号，系统唯一标识，由系统自动生成，格式为"M" + 当前时间戳 |
| **mchName** | String | 商户全称，用于系统内展示和识别 |
| **mchShortName** | String | 商户简称，用于前端界面显示 |
| **type** | Byte | 商户类型：1-普通商户，2-特约商户（服务商模式） |
| **isvNo** | String | 服务商号，当商户类型为特约商户时，关联的服务商编号 |
| **agentNo** | String | 所属代理商号，标识该商户归属于哪个代理商 |

### 联系人信息字段

| 字段名 | 类型 | 业务含义 |
| :--- | :--- | :--- |
| **contactName** | String | 商户联系人姓名，用于商务沟通 |
| **contactTel** | String | 联系人手机号，用于接收系统通知和验证 |
| **contactEmail** | String | 联系人邮箱，用于接收系统邮件和通知 |

### 状态与元数据字段

| 字段名 | 类型 | 业务含义 |
| :--- | :--- | :--- |
| **state** | Byte | 商户状态：0-停用，1-正常。停用状态的商户无法进行交易 |
| **remark** | String | 商户备注，用于记录内部信息 |
| **initUserId** | Long | 初始用户ID，创建商户时自动创建的管理员用户ID |
| **createdUid** | Long | 创建者用户ID，记录创建该商户的操作员 |
| **createdBy** | String | 创建者姓名，记录创建该商户的操作员姓名 |
| **createdAt** | Date | 创建时间，记录商户创建的精确时间 |
| **updatedAt** | Date | 更新时间，每次修改商户信息时自动更新 |

**Section sources**
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java#L24-L140)

## 商户信息管理API接口

系统提供了RESTful API接口用于管理商户信息，主要由`MchInfoController`类实现。该控制器提供了商户的增删改查操作，支持分页查询和条件筛选。

### 查询商户列表

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant 控制器 as MchInfoController
participant 服务层 as MchInfoService
participant 数据库 as 数据库
客户端->>控制器 : GET /api/mchInfo (带查询参数)
控制器->>控制器 : getObject(MchInfo.class) 解析参数
控制器->>控制器 : 构建LambdaQueryWrapper查询条件
控制器->>服务层 : mchInfoService.page(getIPage(), wrapper)
服务层->>数据库 : 执行分页查询
数据库-->>服务层 : 返回分页结果
服务层-->>控制器 : 返回IPage<MchInfo>
控制器->>控制器 : ApiPageRes.pages(pages) 封装响应
控制器-->>客户端 : 返回ApiPageRes<MchInfo>
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L60-L96)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L53-L100)

### 新增商户

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant 控制器 as MchInfoController
participant 服务层 as MchInfoService
participant 用户服务 as SysUserService
participant 数据库 as 数据库
客户端->>控制器 : POST /api/mchInfo (商户信息)
控制器->>控制器 : 获取商户登录名和密码
控制器->>控制器 : 设置商户号、创建者信息
控制器->>服务层 : mchInfoService.addMch(mchInfo, loginUserName)
服务层->>服务层 : 校验服务商状态如为特约商户
服务层->>数据库 : 保存商户基本信息
服务层->>用户服务 : 创建商户管理员用户
用户服务->>数据库 : 保存用户信息
用户服务->>数据库 : 保存用户认证信息默认密码
服务层->>服务层 : 创建商户默认应用
服务层->>服务层 : 更新商户的initUserId
服务层-->>控制器 : 操作成功
控制器-->>客户端 : 返回ApiRes.ok()
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L103-L132)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L134-L203)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L46-L99)

### 更新商户信息

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant 控制器 as MchInfoController
participant 服务层 as MchInfoService
participant 用户服务 as SysUserAuthService
participant MQ as 消息队列
客户端->>控制器 : PUT /api/mchInfo/{mchNo}
控制器->>控制器 : 获取商户信息和更新参数
控制器->>控制器 : 设置商户号主键
控制器->>控制器 : 防止变更商户类型和服务商号
控制器->>控制器 : 检查是否需要重置密码
控制器->>用户服务 : sysUserAuthService.resetAuthInfo(...)
用户服务-->>控制器 : 密码重置成功
控制器->>控制器 : 检查是否禁用商户
控制器->>控制器 : 收集需要清除缓存的用户ID
控制器->>MQ : 发送CleanMchLoginAuthCacheMQ
控制器->>服务层 : mchInfoService.updateById(mchInfo)
服务层->>数据库 : 更新商户信息
服务层-->>控制器 : 更新成功
控制器->>MQ : 发送ResetIsvMchAppInfoConfigMQ
控制器-->>客户端 : 返回ApiRes.ok()
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L163-L228)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L209-L236)

### 删除商户

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant 控制器 as MchInfoController
participant 服务层 as MchInfoService
participant MQ as 消息队列
客户端->>控制器 : DELETE /api/mchInfo/{mchNo}
控制器->>服务层 : mchInfoService.removeByMchNo(mchNo)
服务层->>服务层 : 检查商户是否存在
服务层->>服务层 : 检查是否存在交易数据
服务层->>服务层 : 删除商户支付通道配置
服务层->>服务层 : 删除商户支付接口参数
服务层->>服务层 : 删除商户应用信息
服务层->>服务层 : 删除商户用户及认证信息
服务层->>服务层 : 删除商户基本信息
服务层-->>控制器 : 返回用户ID列表
控制器->>MQ : 发送CleanMchLoginAuthCacheMQ
控制器->>MQ : 发送ResetIsvMchAppInfoConfigMQ
控制器-->>客户端 : 返回ApiRes.ok()
```

**Diagram sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L139-L156)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L243-L279)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L102-L165)

**Section sources**
- [MchInfoController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/merchant/MchInfoController.java#L45-L254)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L39-L281)

## 代理商商户生命周期管理

代理商通过其专属的`MchInfoController`来管理其下级商户的全生命周期，包括创建、查询、更新和删除操作。系统通过`AgentMchRelation`表维护代理商与商户之间的关系。

### 权限控制机制

代理商只能管理其直接或间接关联的商户。在查询、更新和删除操作中，系统会首先验证当前操作的代理商是否有权限访问目标商户。

```mermaid
flowchart TD
A[开始] --> B{检查权限}
B --> |有权限| C[执行操作]
B --> |无权限| D[返回错误]
C --> E[返回成功]
D --> F[返回ApiRes.fail]
```

**Section sources**
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L53-L100)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L106-L128)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L209-L236)
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L243-L279)

### 商户审核流程

当代理商创建新商户时，系统会自动完成以下审核流程：
1. **信息完整性校验**：确保必填字段（如商户名称、联系人信息）已填写。
2. **服务商状态校验**：如果创建的是特约商户，则检查关联的服务商状态是否为正常。
3. **唯一性校验**：确保商户号、登录名等关键信息不重复。

### 商户状态变更

代理商可以启用或禁用其管理的商户。当商户状态变更为"停用"时，系统会自动清除该商户所有用户的登录缓存，确保用户无法继续登录。

**Section sources**
- [MchInfoController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/merchant/MchInfoController.java#L209-L236)

## 数据校验与异常处理

系统在商户信息管理过程中实施了严格的数据校验和异常处理机制，确保数据的完整性和系统的稳定性。

### 数据校验规则

| 操作 | 校验规则 |
| :--- | :--- |
| **新增商户** | - 商户名称、简称、联系人姓名、手机号为必填项<br>- 登录用户名和手机号在系统内必须唯一<br>- 如果是特约商户，服务商号必须存在且状态正常 |
| **更新商户** | - 不能修改商户类型和服务商号<br>- 如果重置密码，新密码必须符合复杂度要求 |
| **删除商户** | - 商户必须存在<br>- 商户不能有交易数据 |

### 异常处理机制

系统使用`BizException`作为业务异常的统一处理方式。当发生业务规则违反时，会抛出带有明确错误信息的`BizException`，由全局异常处理器捕获并转换为标准的API响应。

```mermaid
stateDiagram-v2
[*] --> 正常流程
正常流程 --> 数据校验
数据校验 --> |校验失败| 抛出BizException
数据校验 --> |校验通过| 执行业务逻辑
执行业务逻辑 --> |操作失败| 抛出BizException
执行业务逻辑 --> |操作成功| 返回成功响应
抛出BizException --> 全局异常处理器
全局异常处理器 --> 返回ApiRes.fail
```

**Diagram sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L46-L99)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L102-L165)
- [SysUserAuthService.java](file://service/src/main/java/com/unipay/service/impl/SysUserAuthService.java#L54-L85)

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)
- [SysUserAuthService.java](file://service/src/main/java/com/unipay/service/impl/SysUserAuthService.java#L24-L104)

## 服务层业务逻辑

`MchInfoService`是商户信息管理的核心服务类，负责处理所有与商户相关的业务逻辑，包括数据持久化、事务管理和与其他服务的交互。

### 新增商户业务逻辑

`addMch`方法是一个典型的事务性操作，包含多个步骤：
1. **校验特约商户信息**：如果商户类型为特约商户，则检查关联的服务商是否存在且状态正常。
2. **保存商户基本信息**：将`MchInfo`对象持久化到数据库。
3. **创建管理员用户**：为新商户创建一个管理员用户，用户名为传入的`loginUserName`，密码为默认密码。
4. **创建默认应用**：为商户创建一个名为"默认应用"的应用，用于后续的支付配置。
5. **更新初始用户ID**：将创建的管理员用户ID回填到商户记录中。

该方法使用`@Transactional`注解确保所有操作在一个数据库事务中完成，任何一步失败都会导致整个事务回滚。

### 删除商户业务逻辑

`removeByMchNo`方法同样是一个复杂的事务操作，需要按顺序清理相关数据：
1. **检查商户存在性**：确认要删除的商户存在。
2. **检查交易数据**：如果商户已有交易数据，则不允许删除。
3. **删除关联配置**：依次删除商户的支付通道、支付接口参数、应用信息等。
4. **删除用户信息**：删除商户的所有用户及其认证信息。
5. **删除商户本身**：最后删除商户基本信息。

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L29-L166)

## 批量导入与性能优化

虽然当前代码未直接实现批量导入功能，但基于现有架构，可以设计高效的批量导入方案。

### 批量导入实现思路

1. **异步处理**：使用消息队列（如MQ）将批量导入任务异步化，避免长时间阻塞HTTP请求。
2. **分批处理**：将大批量数据分割成小批次，每批处理固定数量的商户，防止内存溢出。
3. **批量插入**：使用MyBatis Plus的`saveBatch`方法进行批量插入，显著提高数据库写入性能。

### 性能优化策略

| 策略 | 描述 |
| :--- | :--- |
| **数据库索引优化** | 在`t_mch_info`表的`mch_no`、`agent_no`、`state`等常用查询字段上建立索引 |
| **Redis缓存** | 将频繁查询的商户信息缓存到Redis，减少数据库访问 |
| **连接池优化** | 配置合理的数据库连接池大小，避免连接耗尽 |
| **异步消息** | 使用消息队列解耦业务操作，如发送通知、更新缓存等非核心操作 |

**Section sources**
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L46-L99)
- [MchInfoService.java](file://service/src/main/java/com/unipay/service/impl/MchInfoService.java#L102-L165)
- [components-mq](file://components/components-mq)