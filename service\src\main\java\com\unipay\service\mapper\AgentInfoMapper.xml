<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unipay.service.mapper.AgentInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.unipay.core.entity.AgentInfo">
        <id column="agent_no" property="agentNo" />
        <result column="agent_name" property="agentName" />
        <result column="agent_short_name" property="agentShortName" />
        <result column="agent_type" property="agentType" />
        <result column="parent_agent_no" property="parentAgentNo" />
        <result column="agent_level" property="agentLevel" />
        <result column="agent_path" property="agentPath" />
        <result column="contact_name" property="contactName" />
        <result column="contact_tel" property="contactTel" />
        <result column="contact_email" property="contactEmail" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="district" property="district" />
        <result column="address" property="address" />
        <result column="profit_rate" property="profitRate" />
        <result column="state" property="state" />
        <result column="remark" property="remark" />
        <result column="init_user_id" property="initUserId" />
        <result column="created_uid" property="createdUid" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        agent_no, agent_name, agent_short_name, agent_type, parent_agent_no, agent_level, agent_path,
        contact_name, contact_tel, contact_email, province, city, district, address, profit_rate,
        state, remark, init_user_id, created_uid, created_by, created_at, updated_at
    </sql>

    <!-- 根据代理商路径查询所有下级代理商 -->
    <select id="selectSubAgentsByPath" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_agent_info
        WHERE agent_path LIKE CONCAT(#{agentPath}, '%')
        AND agent_path != #{agentPath}
        ORDER BY agent_level, created_at
    </select>

    <!-- 根据上级代理商号查询直属下级代理商 -->
    <select id="selectDirectSubAgents" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM t_agent_info
        WHERE parent_agent_no = #{parentAgentNo}
        ORDER BY created_at DESC
    </select>

    <!-- 更新代理商路径 -->
    <update id="updateAgentPath">
        UPDATE t_agent_info
        SET agent_path = #{agentPath}, updated_at = NOW()
        WHERE agent_no = #{agentNo}
    </update>

</mapper>
