
# 退款申请

<cite>
**本文档引用文件**   
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [RefundOrder.java](file://core/src/main/java/com/unipay/core/entity/RefundOrder.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [IRefundService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IRefundService.java)
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java)
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
</cite>

## 目录
1. [退款申请流程概述](#退款申请流程概述)
2. [API接口定义](#api接口定义)
3. [请求参数说明](#请求参数说明)
4. [请求示例](#请求示例)
5. [响应参数说明](#响应参数说明)
6. [业务规则检查](#业务规则检查)
7. [退款订单创建流程](#退款订单创建流程)
8. [部分退款与全额退款处理](#部分退款与全额退款处理)
9. [退款单与原始支付订单关联机制](#退款单与原始支付订单关联机制)
10. [常见错误码说明](#常见错误码说明)
11. [状态转换流程图](#状态转换流程图)

## 退款申请流程概述

退款申请流程始于商户系统向支付平台发起退款请求。`RefundOrderController`接收退款请求后，首先进行参数校验和权限验证，然后检查业务规则（如退款金额不能超过原订单金额），接着通过`RefundOrderProcessService`创建退款订单并调用相应支付渠道的`IRefundService`实现发起退款。

该流程涉及多个核心组件：`RefundOrderController`负责接收和处理HTTP请求；`RefundOrderService`负责退款订单的持久化操作；`IRefundService`接口定义了与第三方支付渠道交互的标准；`PayMchNotifyService`负责在退款完成后向商户系统发送异步通知。

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L36-L256)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java#L28-L177)
- [IRefundService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IRefundService.java#L13-L27)

## API接口定义

| 属性 | 说明 |
|------|------|
| **请求地址** | `/api/refund/refundOrder` |
| **请求方法** | `POST` |
| **请求协议** | `HTTP/HTTPS` |
| **数据格式** | `JSON` |
| **签名验证** | 需要商户签名验证 |
| **幂等性** | 支持，通过商户退款单号保证 |

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)

## 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| **mchNo** | String | 是 | 商户号 |
| **appId** | String | 是 | 应用ID |
| **mchOrderNo** | String | 否 | 商户订单号（与payOrderId二选一） |
| **payOrderId** | String | 否 | 支付系统订单号（与mchOrderNo二选一） |
| **mchRefundNo** | String | 是 | 商户退款单号 |
| **refundAmount** | Long | 是 | 退款金额，单位：分 |
| **currency** | String | 是 | 三位货币代码，如：CNY |
| **refundReason** | String | 是 | 退款原因 |
| **clientIp** | String | 否 | 客户端IP |
| **notifyUrl** | String | 否 | 异步通知地址 |
| **channelExtra** | String | 否 | 特定渠道发起时额外参数 |
| **extParam** | String | 否 | 扩展参数 |

**Section sources**
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java#L14-L52)

## 请求示例

```json
{
  "mchNo": "MCH123456789",
  "appId": "APP987654321",
  "mchOrderNo": "ORDER20231201001",
  "mchRefundNo": "REFUND20231201001",
  "refundAmount": 1000,
  "currency": "CNY",
  "refundReason": "客户取消订单",
  "clientIp": "***********",
  "notifyUrl": "https://yourdomain.com/notify/refund",
  "channelExtra": "{\"key\":\"value\"}",
  "extParam": "{\"source\":\"web\"}"
}
```

**Section sources**
- [RefundOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRQ.java#L14-L52)

## 响应参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| **refundOrderId** | String | 支付系统退款订单号 |
| **mchRefundNo** | String | 商户发起的退款订单号 |
| **payAmount** | Long | 订单支付金额，单位：分 |
| **refundAmount** | Long | 申请退款金额，单位：分 |
| **state** | Byte | 退款状态：0-订单生成,1-退款中,2-退款成功,3-退款失败 |
| **channelOrderNo** | String | 渠道退款单号 |
| **errCode** | String | 渠道返回错误代码 |
| **errMsg** | String | 渠道返回错误信息 |

**Section sources**
- [RefundOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/refund/RefundOrderRS.java#L12-L53)

## 业务规则检查

在处理退款请求时，系统会执行一系列业务规则检查以确保交易的合法性和安全性：

1. **订单存在性验证**：通过商户号、支付订单号或商户订单号查询原始支付订单，若不存在则拒绝退款。
2. **订单状态验证**：只有支付成功的订单（`PayOrder.STATE_SUCCESS`）才能发起退款。
3. **全额退款验证**：若订单已全额退款（`PayOrder.REFUND_STATE_ALL`）或累计退款金额已达支付金额，则拒绝新的退款请求。
4. **金额超限验证**：确保本次申请的退款金额加上已退款金额不超过原始支付金额。
5. **在途退款验证**：检查是否存在针对同一支付订单的在途退款申请（状态为退款中）。
6. **退款单号唯一性验证**：确保同一商户下的商户退款单号不重复。

```mermaid
flowchart TD
Start([开始]) --> CheckEmpty["检查mchOrderNo和payOrderId<br/>是否同时为空"]
CheckEmpty --> |是| ReturnError1["返回错误：参数不能为空"]
CheckEmpty --> |否| CheckUrl["验证notifyUrl协议是否为<br/>http://或https://"]
CheckUrl --> |无效| ReturnError2["返回错误：通知地址协议无效"]
CheckUrl --> |有效| QueryPayOrder["查询支付订单"]
QueryPayOrder --> |不存在| ReturnError3["返回错误：退款订单不存在"]
QueryPayOrder --> |存在| CheckState["检查订单状态是否为成功"]
CheckState --> |非成功| ReturnError4["返回错误：订单状态不正确"]
CheckState --> |成功| CheckFullRefund["检查是否已全额退款"]
CheckFullRefund --> |已全额| ReturnError5["返回错误：订单已全额退款"]
CheckFullRefund --> |未全额| CheckAmount["检查退款金额是否超限"]
CheckAmount --> |超限| ReturnError6["返回错误：申请金额超出可退款余额"]
CheckAmount --> |未超限| CheckInProcess["检查是否存在在途退款"]
CheckInProcess --> |存在| ReturnError7["返回错误：存在在途退款申请"]
CheckInProcess --> |不存在| CheckSumAmount["检查累计退款金额是否超限"]
CheckSumAmount --> |超限| ReturnError8["返回错误：申请金额超出可退款余额"]
CheckSumAmount --> |未超限| CheckDuplicate["检查商户退款单号是否重复"]
CheckDuplicate --> |重复| ReturnError9["返回错误：商户退款单号已存在"]
CheckDuplicate --> |不重复| Continue["继续处理"]
```

**Diagram sources **
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)

**Section sources**
- [RefundOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/RefundOrderController.java#L47-L157)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L40-L48)

## 退款订单创建流程

退款订单创建流程由`RefundOrderController`的`refundOrder`方法驱动，主要步骤如下：

1. **参数获取与验签**：从请求中获取`RefundOrderRQ`对象并进行商户签名验证。
2. **业务规则检查**：执行上述各项业务规则检查。
3. **商户信息获取**：通过`ConfigContextQueryService`查询商户和应用配置信息。
4. **退款接口获取**：根据支付订单的接口代码动态获取对应的`IRefundService`实现。
5. **退款订单生成**：调用`genRefundOrder`方法创建`RefundOrder`对象，设置各项属性。
6. **退款订单入库**：将退款订单保存到数据库，状态为"订单生成"（`STATE_INIT`）。
7. **调用渠道退款**：通过`IRefundService`的`refund`方法调用第三方支付渠道的退款接口。
8. **状态处理**：根据渠道返回结果，通过`processChannelMsg`方法更新退款订单状态。
9. **返回响应**：构建`RefundOrderRS`响应对象并返回给商户系统。

```mermaid
sequenceDiagram
    participant 商户系统
    participant RefundOrderController
    participant RefundOrderService
    participant IRefundService
    participant 支付渠道
    
    商户系统->