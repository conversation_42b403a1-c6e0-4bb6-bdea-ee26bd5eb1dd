# 关闭订单

<cite>
**本文档引用文件**  
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java)
- [ClosePayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRS.java)
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [IPayOrderCloseService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderCloseService.java)
- [WxpayPayOrderCloseService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPayOrderCloseService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
</cite>

## 目录
1. [请求对象设计](#请求对象设计)
2. [响应对象结构](#响应对象结构)
3. [订单关闭流程](#订单关闭流程)
4. [事务一致性处理](#事务一致性处理)
5. [异常处理与重试机制](#异常处理与重试机制)

## 请求对象设计

`ClosePayOrderRQ` 类定义了关闭订单请求所需的参数，继承自 `AbstractMchAppRQ`，包含商户订单号和支付系统订单号两个核心字段。

```mermaid
classDiagram
class ClosePayOrderRQ {
+String mchOrderNo
+String payOrderId
}
ClosePayOrderRQ --|> AbstractMchAppRQ : 继承
```

**图示来源**  
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java#L12-L21)

**本节来源**  
- [ClosePayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRQ.java#L1-L21)

## 响应对象结构

`ClosePayOrderRS` 类定义了关闭订单响应的数据结构，继承自 `AbstractRS`，主要包含上游渠道返回的数据包 `channelRetMsg`，该字段在序列化时会被忽略。

```mermaid
classDiagram
class ClosePayOrderRS {
-ChannelRetMsg channelRetMsg
}
ClosePayOrderRS --|> AbstractRS : 继承
```

**图示来源**  
- [ClosePayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRS.java#L14-L21)

**本节来源**  
- [ClosePayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/ClosePayOrderRS.java#L1-L21)

## 订单关闭流程

`PayOrderProcessService` 负责协调订单关闭的整个流程。首先通过 `CloseOrderController` 接收请求并验证签名，然后查询订单状态。只有处于“订单生成”（STATE_INIT）或“支付中”（STATE_ING）状态的订单才允许关闭。

当订单处于“订单生成”状态时，直接调用 `PayOrderService` 的 `updateInit2Close` 方法更新本地订单状态为“订单关闭”（STATE_CLOSED）。若订单处于“支付中”状态，则需通过 Spring 容器获取对应的 `IPayOrderCloseService` 实现，调用其 `close` 方法与支付渠道交互。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "CloseOrderController"
participant Service as "PayOrderProcessService"
participant PayOrderService as "PayOrderService"
participant ChannelService as "IPayOrderCloseService"
Client->>Controller : 发送关闭订单请求
Controller->>Controller : 验证参数和签名
Controller->>PayOrderService : 查询订单
PayOrderService-->>Controller : 返回订单信息
Controller->>Controller : 验证订单状态
alt 订单状态为STATE_INIT
Controller->>PayOrderService : updateInit2Close
PayOrderService-->>Controller : 更新成功
Controller->>Client : 返回成功响应
else 订单状态为STATE_ING
Controller->>Service : 获取支付渠道关闭服务
Service-->>Controller : 返回IPayOrderCloseService
Controller->>ChannelService : 调用close方法
ChannelService-->>Controller : 返回ChannelRetMsg
Controller->>PayOrderService : updateIng2Close
PayOrderService-->>Controller : 更新成功
Controller->>Client : 返回成功响应
end
```

**图示来源**  
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java#L39-L105)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L78-L85)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L88-L95)

**本节来源**  
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java#L39-L105)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)

## 事务一致性处理

系统通过 `@Transactional` 注解确保关键操作的原子性。在更新订单状态时，使用 `LambdaUpdateWrapper` 构造精确的更新条件，例如将状态从 `STATE_ING` 更新为 `STATE_CLOSED` 时，必须确保当前状态确实是 `STATE_ING`，从而防止并发更新导致的状态不一致问题。

本地订单状态的更新与渠道操作的协调通过严格的顺序控制实现：先执行渠道关闭操作，待收到明确的成功响应（`ChannelState.CONFIRM_SUCCESS`）后，再更新本地订单状态。这种“先外后内”的策略保证了状态的一致性。

```mermaid
flowchart TD
A[开始] --> B{订单状态?}
B --> |STATE_INIT| C[直接更新本地状态为CLOSED]
B --> |STATE_ING| D[调用渠道close接口]
D --> E{渠道返回状态?}
E --> |CONFIRM_SUCCESS| F[更新本地状态为CLOSED]
E --> |其他状态| G[返回失败]
C --> H[返回成功]
F --> H
G --> I[返回失败]
```

**图示来源**  
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L78-L85)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L88-L95)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L30-L35)

**本节来源**  
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L67-L95)

## 异常处理与重试机制

在关闭订单过程中，任何异常都会被捕获并记录日志，然后返回 `null` 或自定义的失败响应。`ChannelRetMsg` 类定义了多种状态，如 `SYS_ERROR` 和 `UNKNOWN`，用于区分系统内部错误和状态未知的情况。

对于关闭失败的情况，系统目前没有内置的自动重试机制，但提供了清晰的错误信息（`channelErrMsg`），供商户系统根据业务需求实现重试逻辑。例如，当收到 `WAITING` 状态时，商户可以稍后发起查询请求来确认最终结果。

```mermaid
classDiagram
class ChannelRetMsg {
+ChannelState channelState
+String channelOrderId
+String channelErrCode
+String channelErrMsg
+static ChannelRetMsg confirmSuccess()
+static ChannelRetMsg confirmFail()
+static ChannelRetMsg waiting()
+static ChannelRetMsg sysError()
}
class ChannelRetMsg : : ChannelState {
CONFIRM_SUCCESS
CONFIRM_FAIL
WAITING
UNKNOWN
SYS_ERROR
}
```

**图示来源**  
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L1-L114)
- [WxpayPayOrderCloseService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPayOrderCloseService.java#L37-L88)

**本节来源**  
- [CloseOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/CloseOrderController.java#L39-L105)
- [WxpayPayOrderCloseService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/wxpay/WxpayPayOrderCloseService.java#L37-L88)