# 目录结构说明

<cite>
**本文档中引用的文件**  
- [application.yml](file://sys-agent/src/main/resources/application.yml)
- [application.yml](file://sys-manager/src/main/resources/application.yml)
- [components-mq/pom.xml](file://components/components-mq/pom.xml)
- [components-oss/pom.xml](file://components/components-oss/pom.xml)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [StringKit.java](file://core/src/main/java/com/unipay/core/utils/StringKit.java)
- [DEPLOYMENT.md](file://z-docs/deploy/DEPLOYMENT.md)
- [系统配置说明.md](file://z-docs/md_records/系统配置说明.md)
- [package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-manager/package.json)
</cite>

## 目录结构说明

本项目采用清晰的分层架构设计，通过合理的目录划分实现了代码复用、模块化开发和独立部署。以下是对各顶级目录职责的详细说明，以及整体结构如何支持系统可维护性和扩展性的分析。

### code-gen（代码生成）
该目录专门用于存放代码生成工具及相关配置。其主要职责是自动化生成重复性代码，如实体类、DAO层接口等，从而提升开发效率并保证代码风格一致性。通过集中管理代码生成逻辑，团队可以快速响应数据库结构变更，减少手动编码错误。

### components（可复用公共组件）
`components` 目录包含多个高内聚、低耦合的公共组件模块，供整个系统共享使用。目前主要包括：
- **components-mq**：消息队列抽象组件，封装了ActiveMQ、RabbitMQ、RocketMQ及阿里云RocketMQ等多种MQ厂商的接入逻辑，提供统一的消息发送与接收接口。
- **components-oss**：对象存储服务组件，支持本地存储、阿里云OSS等多种存储方式，提供统一的文件上传、下载和管理功能。

这些组件通过Maven依赖方式被其他服务引用，实现了跨服务的功能复用。例如，`components-mq` 被所有 `sys-*` 微服务所依赖，确保消息通信机制的一致性。

### core（核心实体与工具类）
`core` 模块是整个系统的基石，包含所有共享的核心资源：
- **entity**：定义了系统中所有持久化实体，如 `SysUser`、`PayOrder`、`MchInfo` 等，被所有后端服务共用。
- **utils**：提供了丰富的通用工具类，如 `StringKit`（字符串处理）、`AmountUtil`（金额计算）、`JsonKit`（JSON操作）等。
- **constants**：全局常量定义，如API返回码、系统类型枚举等。
- **exception**：统一异常处理机制，包括 `BizException` 和全局异常处理器。
- **cache**：基于Redis的缓存工具 `RedisUtil` 和令牌服务接口。
- **model**：通用数据模型和请求/响应封装类，如 `ApiRes`、`BaseModel`。

该模块被所有后端服务（`sys-*`）直接依赖，确保了数据结构和基础逻辑的高度一致性。

### service（数据访问层）
`service` 目录实现了完整的业务逻辑与数据持久化操作，采用典型的三层架构：
- **mapper**：MyBatis映射接口，定义数据库操作方法。
- **impl**：服务实现类，封装具体业务逻辑。
- 实体类映射XML文件集中存放于 `resources/mapper`。

此模块为上层微服务提供统一的数据访问入口，避免了各服务直接操作数据库带来的冗余和不一致问题。

### sys-*（独立微服务应用）
以 `sys-` 前缀命名的目录代表独立部署的微服务应用，每个均为Spring Boot项目，具备完整的服务能力：
- **sys-agent**：代理商平台，端口9219，面向代理商用户。
- **sys-manager**：运营平台，端口8080（可配置），供系统管理员使用。
- **sys-merchant**：商户平台，端口未显式配置，默认继承父级设置。
- **sys-payment**：支付网关服务，端口9216，处理支付核心流程。

每个微服务都依赖 `core` 模块获取实体和工具类，并通过 `components` 使用MQ和OSS功能。它们各自拥有独立的控制器（`ctrl`）、安全配置（`secruity`）、消息监听器（`mq`）和服务逻辑（`service`），实现了职责分离和独立部署。

### unipay-web-ui（前端UI）
该目录包含所有前端用户界面，采用Vue 3 + Vite技术栈构建，分为多个子应用：
- **unipay-ui-agent**：代理商平台前端，对应 `sys-agent` 后端。
- **unipay-ui-manager**：运营平台前端，对应 `sys-manager` 后端。
- **unipay-ui-merchant**：商户平台前端，对应 `sys-merchant` 后端。
- **unipay-ui-cashier**：收银台页面，集成在支付流程中。

每个子应用均为独立的Vue项目，拥有自己的 `package.json`、路由配置和状态管理。构建产物（dist）由对应后端服务通过 `static-locations` 配置进行静态资源托管，实现前后端一体化部署。

### z-docs（文档与脚本）
`z-docs` 是项目文档和运维脚本的集中地，包含：
- **deploy/**：部署相关文档与脚本，如 `DEPLOYMENT.md` 提供了Nginx反向代理和SSL证书配置指南。
- **md_records/**：系统配置说明、业务流程文档等。
- **script/**：初始化数据库、启动脚本等自动化工具。
- **sql/**：数据库初始化脚本和补丁。

该目录为新开发者提供了完整的环境搭建和运维支持，降低了项目上手门槛。

## 分层结构优势分析

### 促进代码复用
通过将公共功能抽象为独立模块（`core`、`components`），避免了代码重复。例如，所有微服务均可直接使用 `core` 中的 `RedisUtil` 进行缓存操作，无需重复实现。

### 支持模块化开发
各 `sys-*` 服务职责明确，开发团队可并行工作于不同平台（运营、商户、代理），互不影响。前端与后端也通过清晰的API契约解耦，支持独立迭代。

### 实现独立部署
每个 `sys-*` 服务可单独打包、部署和伸缩。结合Nginx反向代理，可通过不同路径（`/manager`、`/merchant`、`/agent`）对外提供服务，满足微服务架构要求。

### 统一配置管理
系统通过数据库表 `t_sys_config` 统一管理各平台URL（如 `mgrSiteUrl`、`mchSiteUrl`），并通过MQ广播机制实时同步配置变更，确保服务间调用地址的准确性。

## 新开发者导航指南

| 功能区域 | 对应代码位置 |
|--------|------------|
| 用户登录认证 | `sys-*/secruity/` + `core/entity/SysUser.java` |
| 支付订单处理 | `sys-payment/channel/` + `service/impl/PayOrderService.java` |
| 消息队列通信 | `components/components-mq/` + `sys-*/mq/` |
| 文件上传下载 | `components/components-oss/` + `unipay-web-ui/*/src/api/` |
| 前端页面开发 | `unipay-web-ui/unipay-ui-*/src/views/` |
| 系统配置管理 | `z-docs/md_records/系统配置说明.md` + `core/service/ISysConfigService.java` |

## 核心模块依赖关系图

```mermaid
graph TD
subgraph "前端应用"
A["unipay-web-ui\n(Vue 3)"]
end
subgraph "微服务层"
B["sys-agent\n(代理商平台)"]
C["sys-manager\n(运营平台)"]
D["sys-merchant\n(商户平台)"]
E["sys-payment\n(支付网关)"]
end
subgraph "公共组件层"
F["components-mq\n(消息队列)"]
G["components-oss\n(对象存储)"]
end
subgraph "核心层"
H["core\n(实体/工具类)"]
end
subgraph "数据访问层"
I["service\n(MyBatis Mapper)"]
end
A --> B
A --> C
A --> D
A --> E
B --> F
B --> G
B --> H
B --> I
C --> F
C --> G
C --> H
C --> I
D --> F
D --> G
D --> H
D --> I
E --> F
E --> G
E --> H
E --> I
F --> H
G --> H
I --> H
style A fill:#4CAF50,stroke:#388E3C
style B fill:#2196F3,stroke:#1976D2
style C fill:#2196F3,stroke:#1976D2
style D fill:#2196F3,stroke:#1976D2
style E fill:#2196F3,stroke:#1976D2
style F fill:#FF9800,stroke:#F57C00
style G fill:#FF9800,stroke:#F57C00
style H fill:#9C27B0,stroke:#7B1FA2
style I fill:#00BCD4,stroke:#00ACC1
```

**图示来源**  
- [application.yml](file://sys-agent/src/main/resources/application.yml)
- [application.yml](file://sys-manager/src/main/resources/application.yml)
- [components-mq/pom.xml](file://components/components-mq/pom.xml)
- [components-oss/pom.xml](file://components/components-oss/pom.xml)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [StringKit.java](file://core/src/main/java/com/unipay/core/utils/StringKit.java)
- [DEPLOYMENT.md](file://z-docs/deploy/DEPLOYMENT.md)
- [系统配置说明.md](file://z-docs/md_records/系统配置说明.md)
- [package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [package.json](file://unipay-web-ui/unipay-ui-manager/package.json)

**本节来源**  
- [sys-agent/src/main/resources/application.yml](file://sys-agent/src/main/resources/application.yml)
- [sys-manager/src/main/resources/application.yml](file://sys-manager/src/main/resources/application.yml)
- [components/components-mq/pom.xml](file://components/components-mq/pom.xml)
- [components/components-oss/pom.xml](file://components/components-oss/pom.xml)
- [core/src/main/java/com/unipay/core/entity/SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [core/src/main/java/com/unipay/core/utils/StringKit.java](file://core/src/main/java/com/unipay/core/utils/StringKit.java)
- [z-docs/deploy/DEPLOYMENT.md](file://z-docs/deploy/DEPLOYMENT.md)
- [z-docs/md_records/系统配置说明.md](file://z-docs/md_records/系统配置说明.md)
- [unipay-web-ui/unipay-ui-agent/package.json](file://unipay-web-ui/unipay-ui-agent/package.json)
- [unipay-web-ui/unipay-ui-manager/package.json](file://unipay-web-ui/unipay-ui-manager/package.json)