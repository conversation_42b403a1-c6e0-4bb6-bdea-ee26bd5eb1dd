# 分账功能

<cite>
**本文档引用的文件**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java)
- [MchDivisionReceiverGroup.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java)
- [SeqKit.java](file://core/src/main/java/com/unipay/core/utils/SeqKit.java)
- [CS.java](file://core/src/main/java/com/unipay/core/constants/CS.java)
</cite>

## 目录
1. [分账模式](#分账模式)
2. [分账处理服务](#分账处理服务)
3. [消息队列机制](#消息队列机制)
4. [分账接收者实体](#分账接收者实体)
5. [分账规则配置](#分账规则配置)
6. [API调用示例](#api调用示例)
7. [失败处理与对账](#失败处理与对账)

## 分账模式

系统支持两种分账模式：自动分账和手动分账。在`PayOrder`实体中，通过`divisionMode`字段定义分账模式，其取值如下：
- `0`：该笔订单不允许分账（`DIVISION_MODE_FORBID`）
- `1`：支付成功后按配置自动完成分账（`DIVISION_MODE_AUTO`）
- `2`：商户手动分账（解冻商户金额）（`DIVISION_MODE_MANUAL`）

自动分账模式下，系统会在支付成功后自动触发分账流程，无需商户手动干预。手动分账模式则需要商户通过API或管理界面主动发起分账请求，适用于需要灵活控制分账时机的业务场景。

**分账状态**通过`divisionState`字段表示，包括：
- `0`：未发生分账（`DIVISION_STATE_UNHAPPEN`）
- `1`：等待分账任务处理（`DIVISION_STATE_WAIT_TASK`）
- `2`：分账处理中（`DIVISION_STATE_ING`）
- `3`：分账任务已结束（`DIVISION_STATE_FINISH`）

**Section sources**
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L100-L118)

## 分账处理服务

`PayOrderDivisionProcessService`是分账功能的核心服务类，负责处理分账请求的完整生命周期，包括分账规则解析、金额计算和请求发送。

### 实现机制

该服务通过`processPayOrderDivision`方法实现分账逻辑，其处理流程如下：

1. **参数验证**：检查订单是否存在且分账状态正确
2. **状态更新**：将订单状态更新为"分账处理中"
3. **接收者查询**：根据配置查询分账接收者列表
4. **金额计算**：计算分账金额并生成分账记录
5. **调用渠道**：调用具体支付渠道的分账接口
6. **结果处理**：根据渠道响应更新分账记录状态
7. **状态回写**：将订单状态更新为"分账任务已结束"

```mermaid
flowchart TD
Start([开始]) --> Validate["验证订单状态"]
Validate --> UpdateStatus["更新为分账处理中"]
UpdateStatus --> CheckResend{"是否重发?"}
CheckResend --> |是| QueryFromDB["从数据库查询记录"]
CheckResend --> |否| QueryReceiver["查询分账接收者"]
QueryReceiver --> CalcAmount["计算分账金额"]
CalcAmount --> GenRecord["生成分账记录"]
GenRecord --> SaveRecord["保存分账记录"]
SaveRecord --> CallChannel["调用渠道分账接口"]
QueryFromDB --> CallChannel
CallChannel --> CheckResult{"渠道响应结果"}
CheckResult --> |成功| UpdateSuccess["更新为分账成功"]
CheckResult --> |失败| UpdateFail["更新为分账失败"]
CheckResult --> |已受理| UpdateAccept["更新为已受理"]
UpdateSuccess --> FinishStatus["更新订单为分账结束"]
UpdateFail --> FinishStatus
UpdateAccept --> FinishStatus
FinishStatus --> End([结束])
```

**Diagram sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)

### 分账金额计算

分账金额的计算基于商家实际入账金额，即支付金额减去手续费、退款金额和已分账金额。计算逻辑在`PayOrderService.calMchIncomeAmount`方法中实现。

```mermaid
classDiagram
class PayOrderService {
+calMchIncomeAmount(PayOrder) Long
}
class AmountUtil {
+calPercentageFee(Long, BigDecimal, int) Long
}
class PayOrderDivisionRecordMapper {
+sumSuccessDivisionAmount(String) Long
}
PayOrderService --> AmountUtil : "使用"
PayOrderService --> PayOrderDivisionRecordMapper : "查询"
```

**Diagram sources**
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L370-L380)
- [AmountUtil.java](file://core/src/main/java/com/unipay/core/utils/AmountUtil.java#L117-L119)
- [PayOrderDivisionRecordMapper.java](file://service/src/main/java/com/unipay/service/mapper/PayOrderDivisionRecordMapper.java#L15-L16)

### 分账记录生成

`genRecord`方法负责生成单条分账记录，包括设置接收者信息、计算分账金额等。为避免金额溢出，系统采用向下取整的方式计算分账金额，并在最后一条记录中处理剩余金额。

**Section sources**
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L192-L230)

## 消息队列机制

系统通过消息队列实现分账功能的异步处理和系统解耦，主要涉及`PayOrderDivisionMQ`消息和`PayOrderDivisionMQReceiver`接收器。

### 消息结构

`PayOrderDivisionMQ`定义了分账消息的结构，包含以下关键字段：

| 字段 | 类型 | 说明 |
|------|------|------|
| payOrderId | String | 支付订单号 |
| useSysAutoDivisionReceivers | Byte | 是否使用默认分组 |
| receiverList | List<CustomerDivisionReceiver> | 分账接收者列表 |
| isResend | Boolean | 是否重新发送 |

其中，`CustomerDivisionReceiver`包含`receiverId`、`receiverGroupId`和`divisionProfit`三个字段，用于指定具体的分账接收者和分账比例。

```mermaid
classDiagram
class PayOrderDivisionMQ {
+MQ_NAME String
+payload MsgPayload
+getMQName() String
+getMQType() MQSendTypeEnum
+toMessage() String
+build(...) PayOrderDivisionMQ
+parse(String) MsgPayload
}
class MsgPayload {
+payOrderId String
+useSysAutoDivisionReceivers Byte
+receiverList List<CustomerDivisionReceiver>
+isResend Boolean
}
class CustomerDivisionReceiver {
+receiverId Long
+receiverGroupId Long
+divisionProfit BigDecimal
}
PayOrderDivisionMQ --> MsgPayload
MsgPayload --> CustomerDivisionReceiver
```

**Diagram sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)

### 消息处理流程

`PayOrderDivisionMQReceiver`实现了`IMQReceiver`接口，负责接收和处理分账消息。其处理流程简单直接：接收消息后，直接调用`PayOrderDivisionProcessService`的`processPayOrderDivision`方法执行分账。

```mermaid
sequenceDiagram
participant MQ as 消息队列
participant Receiver as PayOrderDivisionMQReceiver
participant Service as PayOrderDivisionProcessService
MQ->>Receiver : 发送分账消息
Receiver->>Receiver : 记录日志
Receiver->>Service : 调用processPayOrderDivision
Service->>Service : 执行分账逻辑
Service-->>Receiver : 返回结果
Receiver-->>MQ : 确认消息处理完成
```

**Diagram sources**
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L21-L31)

## 分账接收者实体

系统通过`MchDivisionReceiver`和`MchDivisionReceiverGroup`两个实体来管理分账接收者，支持灵活的分账配置。

### MchDivisionReceiver

`MchDivisionReceiver`表示单个分账接收者，包含以下关键属性：

| 属性 | 说明 |
|------|------|
| receiverId | 分账接收者ID |
| receiverAlias | 接收者账号别名 |
| receiverGroupId | 组ID（便于商户接口使用） |
| mchNo | 商户号 |
| appId | 应用ID |
| ifCode | 支付接口代码 |
| accType | 分账接收账号类型：0-个人(对私)，1-商户(对公) |
| accNo | 分账接收账号 |
| accName | 分账接收账号名称 |
| relationType | 分账关系类型（参考微信），如：SERVICE_PROVIDER 服务商等 |
| divisionProfit | 分账比例 |
| state | 分账状态：1-正常分账，0-暂停分账 |

该实体通过`mchNo`、`appId`和`ifCode`进行多维度关联，确保分账配置的精确性。

**Section sources**
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java#L23-L159)

### MchDivisionReceiverGroup

`MchDivisionReceiverGroup`表示分账接收者组，用于批量管理分账接收者，其主要属性包括：

| 属性 | 说明 |
|------|------|
| receiverGroupId | 组ID |
| receiverGroupName | 组名称 |
| mchNo | 商户号 |
| autoDivisionFlag | 自动分账组标志：0-否，1-是 |

当`autoDivisionFlag`为1时，该组被标记为自动分账组，在自动分账模式下将使用该组的接收者进行分账。

```mermaid
classDiagram
class MchDivisionReceiverGroup {
+receiverGroupId Long
+receiverGroupName String
+mchNo String
+autoDivisionFlag Byte
+createdUid Long
+createdBy String
+createdAt Date
+updatedAt Date
}
class MchDivisionReceiver {
+receiverId Long
+receiverAlias String
+receiverGroupId Long
+mchNo String
+isvNo String
+appId String
+ifCode String
+accType Byte
+accNo String
+accName String
+relationType String
+relationTypeName String
+divisionProfit BigDecimal
+state Byte
+channelBindResult String
+channelExtInfo String
+bindSuccessTime Date
+createdAt Date
+updatedAt Date
}
MchDivisionReceiverGroup "1" --> "0..*" MchDivisionReceiver : "包含"
```

**Diagram sources**
- [MchDivisionReceiverGroup.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiverGroup.java#L22-L87)
- [MchDivisionReceiver.java](file://core/src/main/java/com/unipay/core/entity/MchDivisionReceiver.java#L23-L159)

## 分账规则配置

配置分账规则需要通过管理界面或API完成，主要步骤如下：

### 1. 创建分账接收者组

首先创建分账接收者组，指定组名称和是否为自动分账组。自动分账组将在自动分账模式下被使用。

### 2. 添加分账接收者

在指定组中添加分账接收者，配置以下信息：
- 接收者账号信息（账号类型、账号、名称）
- 分账关系类型
- 分账比例
- 状态（启用/暂停）

### 3. 关联支付接口

确保分账接收者与特定的支付接口代码（ifCode）关联，以便在调用该接口时使用正确的分账配置。

### 4. 设置订单分账模式

在创建支付订单时，通过`divisionMode`参数设置分账模式：
- `1`：自动分账
- `2`：手动分账

## API调用示例

### 发起手动分账

```java
// 构建分账消息
PayOrderDivisionMQ mq = PayOrderDivisionMQ.build(
    "PAY123456789",           // 支付订单号
    CS.YES,                   // 使用系统默认分组
    null,                     // 接收者列表（null表示使用默认配置）
    false                     // 是否重发
);

// 发送消息到队列
mqSender.send(mq);
```

### 自定义分账接收者

```java
// 创建自定义接收者列表
List<PayOrderDivisionMQ.CustomerDivisionReceiver> receivers = new ArrayList<>();
receivers.add(new PayOrderDivisionMQ.CustomerDivisionReceiver(
    1001L,                    // receiverId
    null,                     // receiverGroupId
    new BigDecimal("0.3")     // divisionProfit
));
receivers.add(new PayOrderDivisionMQ.CustomerDivisionReceiver(
    null,                     // receiverId
    2001L,                    // receiverGroupId
    null                      // 使用系统默认比例
));

// 构建分账消息
PayOrderDivisionMQ mq = PayOrderDivisionMQ.build(
    "PAY123456789",
    CS.NO,                    // 不使用系统默认分组
    receivers,
    false
);

// 发送消息
mqSender.send(mq);
```

**Section sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L85-L105)

## 失败处理与对账

### 失败处理策略

系统对分账失败情况提供了完善的处理机制：

1. **状态管理**：分账记录有明确的状态标识（成功、失败、已受理）
2. **错误记录**：保存渠道返回的错误码和错误信息
3. **重试机制**：支持通过`isResend`参数重新发起分账请求

当分账失败时，系统会更新分账记录状态为"分账失败"，并记录详细的错误信息，便于后续排查。

### 重试机制

重试机制通过`PayOrderDivisionRecordService.updateResendState`方法实现，主要步骤包括：
1. 重新生成分账批次号，避免`out_trade_no`重复
2. 将分账记录状态重置为"待分账"
3. 清空渠道返回结果和批次号
4. 更新订单分账状态为"等待分账任务处理"

### 对账流程

对账流程建议如下：

1. **数据核对**：定期核对系统分账记录与渠道分账结果
2. **状态同步**：对于状态为"已受理"的分账记录，主动查询渠道确认最终结果
3. **异常处理**：发现不一致时，通过重试机制重新处理
4. **报表生成**：生成分账对账报表，包括成功、失败、待确认的分账记录

系统通过`PayOrderDivisionRecord`表保存了完整的分账快照，包括分账时的账号信息、分账比例和计算金额，确保对账数据的准确性和可追溯性。

**Section sources**
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L42-L57)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L37-L39)