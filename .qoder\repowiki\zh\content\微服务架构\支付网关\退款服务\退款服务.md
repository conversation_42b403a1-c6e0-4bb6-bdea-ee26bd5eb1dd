# 退款服务

## 退款请求处理流程

退款服务的核心流程始于 `RefundOrderController` 接收商户发起的退款请求。该控制器位于 `sys-payment` 模块中，负责处理 `/api/refund/refundOrder` 接口的 POST 请求。

### 请求接收与参数校验

`RefundOrderController` 首先通过 `getRQByWithMchSign(RefundOrderRQ.class)` 方法获取并验证请求参数。`RefundOrderRQ` 对象封装了所有必要的退款信息，包括：
- `mchNo` (商户号) 和 `appId` (应用ID)
- `payOrderId` 或 `mchOrderNo` (支付系统订单号或商户订单号)
- `mchRefundNo` (商户退款单号)
- `refundAmount` (退款金额，单位为分)
- `currency` (货币代码)
- `refundReason` (退款原因)
- `notifyUrl` (异步通知地址)

在调用具体退款服务前，控制器会进行一系列严格的业务校验，确保退款请求的合法性：
1.  **订单存在性校验**：通过 `payOrderService.queryMchOrder()` 查询原始支付订单，若订单不存在则拒绝请求。
2.  **订单状态校验**：原始支付订单必须处于“支付成功”（`PayOrder.STATE_SUCCESS`）状态。
3.  **退款状态校验**：原始订单不能是“已全额退款”状态。
4.  **金额校验**：本次申请的退款金额加上已退款金额，不能超过原始支付订单的总金额。
5.  **重复请求校验**：检查是否存在相同商户号和商户退款单号的在途退款申请，防止重复提交。
6.  **商户退款单号唯一性校验**：确保商户退款单号在该商户下是唯一的。

### 退款单创建与状态管理

通过所有校验后，系统会调用 `genRefundOrder()` 方法创建一个 `RefundOrder` 实体对象。该对象代表了系统内部的退款单，其核心字段包括：
- `refundOrderId`：系统生成的唯一退款订单号。
- `payOrderId`：关联的原始支付订单号。
- `mchNo`, `appId`, `ifCode`：商户、应用及支付接口信息。
- `payAmount` 和 `refundAmount`：原始支付金额和本次退款金额。
- `state`：退款状态，初始为“订单生成”（`RefundOrder.STATE_INIT`）。
- `expiredTime`：退款失效时间，通常为创建时间后2小时。

创建完成后，`refundOrderService.save(refundOrder)` 将退款单持久化到数据库 `t_refund_order` 表中。

### 协调调用具体支付渠道

退款单创建成功后，`RefundOrderController` 开始协调调用具体的支付渠道服务。其核心逻辑如下：
1.  **获取渠道服务**：通过 `SpringBeansUtil.getBean(payOrder.getIfCode() + "RefundService", IRefundService.class)` 从 Spring 容器中动态获取对应支付接口（如支付宝、微信）的 `IRefundService` 实现。
2.  **调用退款接口**：调用 `refundService.refund()` 方法，并传入 `RefundOrderRQ`、`RefundOrder`、`PayOrder` 和 `MchAppConfigContext`（商户配置上下文）等参数。
3.  **处理渠道响应**：根据 `refundService` 返回的 `ChannelRetMsg` 对象，调用 `processChannelMsg()` 方法来更新退款单的状态。

`IRefundService` 是一个接口，定义了所有支付渠道退款服务必须实现的方法，包括 `refund()` 和 `query()`。具体的实现类如 `AlipayRefundService` 和 `WxpayRefundService` 会根据各自支付平台的API规范，构造请求并调用上游接口。

#### 支付宝退款实现 (`AlipayRefundService`)
该服务调用支付宝的 `alipay.trade.refund` 接口。它会设置 `out_trade_no`（支付订单号）、`trade_no`（渠道支付单号）和 `out_request_no`（退款单号）等关键参数。如果调用成功，`ChannelRetMsg` 的状态为 `CONFIRM_SUCCESS`；如果失败，则为 `CONFIRM_FAIL`。

#### 微信退款实现 (`WxpayRefundService`)
该服务根据微信API版本（V2或V3）调用不同的接口。V2版本调用 `refund` 接口，V3版本调用 `v3/refund/domestic/refund` 接口。其返回的 `ChannelRetMsg` 状态同样用于指示退款结果。

### 退款结果异步通知机制

当支付渠道的退款操作完成后，渠道会向系统发起异步回调通知。此功能由 `ChannelRefundNoticeController` 处理，其接口地址为 `/api/refund/notify/{ifCode}`。

1.  **接收通知**：控制器根据 `ifCode` 动态获取对应的 `IChannelRefundNoticeService` 实现（如 `AlipayChannelRefundNoticeService`）。
2.  **解析与验签**：调用 `parseParams()` 方法解析请求参数和订单号，并进行签名验证，确保通知的合法性。
3.  **处理业务逻辑**：调用 `doNotice()` 方法处理通知。该方法会验证通知中的退款单号与系统记录是否一致。
4.  **更新订单状态**：将解析出的 `ChannelRetMsg` 传递给 `RefundOrderProcessService.handleRefundOrder4Channel()` 方法。该服务会根据 `ChannelRetMsg` 的状态（成功或失败）更新 `RefundOrder` 的状态，并调用 `payMchNotifyService.refundOrderNotify()` 向商户的 `notifyUrl` 发送最终的退款结果通知。

### 对账处理

对账处理主要通过定时任务 `RefundOrderReissueTask` 来完成。该任务每分钟执行一次，查询所有状态为“退款中”（`RefundOrder.STATE_ING`）的退款单。

对于每一条在途退款单，任务会调用 `channelOrderReissueService.processRefundOrder()` 方法。该方法会：
1.  根据退款单的 `ifCode` 获取对应的 `IRefundService`。
2.  调用 `refundService.query()` 方法向支付渠道发起查单请求。
3.  将查单结果（`ChannelRetMsg`）传递给 `RefundOrderProcessService`，由其更新退款单的最终状态。

此机制确保了即使渠道异步通知丢失或失败，系统也能通过主动查询的方式最终确认退款结果，保证了账务的准确性。

## 业务规则与重试机制

### 部分退款与多次退款

本系统支持对同一笔支付订单进行多次部分退款，直至总退款金额达到原始支付金额，实现全额退款。

-   **部分退款**：当一笔支付订单的可退款余额大于0时，可以发起部分退款。每次退款后，原始支付订单的 `refundAmount` 字段会累加，`refundTimes` 字段会递增。当 `refundAmount` 等于 `amount` 时，`refundState` 会更新为“全额退款”（`PayOrder.REFUND_STATE_ALL`）。
-   **多次退款**：系统通过 `refundOrderService.count()` 方法检查是否存在同一支付订单的在途退款申请，以防止并发请求导致的重复退款。同时，通过 `sumSuccessRefundAmount()` 方法计算历史成功退款的总金额，确保新的退款申请不会超出可退款余额。

### 退款失败重试机制

系统通过 `RefundOrderReissueTask` 定时任务实现了退款失败的重试机制。

-   **任务触发**：当 `RefundOrderController` 调用 `refundService.refund()` 时，如果上游渠道返回“处理中”（`WAITING`）或“未知”（`UNKNOWN`）状态，退款单状态会被更新为“退款中”（`STATE_ING`）。
-   **自动重试**：`RefundOrderReissueTask` 会定期扫描所有“退款中”状态的退款单，并调用 `IRefundService.query()` 方法进行查单。
-   **状态同步**：查单结果会通过 `RefundOrderProcessService` 同步回 `RefundOrder`，将其状态更新为“退款成功”或“退款失败”。如果查单结果仍为“处理中”，则该退款单会继续留在队列中等待下一次轮询。

此机制确保了系统能够最终获取到所有退款操作的确定性结果，保障了业务的最终一致性。

### 退款单状态管理

`RefundOrder` 实体定义了完整的退款生命周期状态：
-   **0 - 订单生成 (STATE_INIT)**：退款单已创建，但尚未调用上游渠道。
-   **1 - 退款中 (STATE_ING)**：已调用上游渠道，正在等待结果。
-   **2 - 退款成功 (STATE_SUCCESS)**：上游渠道明确返回退款成功。
-   **3 - 退款失败 (STATE_FAIL)**：上游渠道明确返回退款失败。
-   **4 - 退款任务关闭 (STATE_CLOSED)**：退款单超时未完成，由系统自动关闭。

状态的转换由 `RefundOrderService` 中的 `updateInit2Ing`、`updateIng2Success` 和 `updateIng2Fail` 等方法保证，这些方法通过数据库乐观锁确保了状态变更的原子性。

### 与原始支付订单的关联关系

退款单与原始支付订单通过 `payOrderId` 字段紧密关联。这种关联关系体现在：
1.  **数据关联**：`RefundOrder` 表中的 `payOrderId` 字段是外键，指向 `t_pay_order` 表的主键。
2.  **状态联动**：当一笔支付订单的所有退款完成后，其 `refundState` 会变为“全额退款”，`state` 会变为“已退款”（`PayOrder.STATE_REFUND`）。
3.  **金额联动**：每次成功退款后，`PayOrder` 的 `refundAmount` 字段会增加，`refundTimes` 字段会递增。

这种设计确保了支付和退款业务在数据层面的完整性和可追溯性。

**Section sources**
- sys-payment\src\main\java\com\unipay\pay\ctrl\refund\RefundOrderController.java
- sys-payment\src\main\java\com\unipay\pay\service\RefundOrderProcessService.java
- sys-payment\src\main\java\com\unipay\pay\channel\IRefundService.java
- sys-payment\src\main\java\com\unipay\pay\channel\alipay\AlipayRefundService.java
- sys-payment\src\main\java\com\unipay\pay\channel\wxpay\WxpayRefundService.java
- sys-payment\src\main\java\com\unipay\pay\ctrl\refund\ChannelRefundNoticeController.java
- sys-payment\src\main\java\com\unipay\pay\task\RefundOrderReissueTask.java
- core\src\main\java\com\unipay\core\entity\RefundOrder.java
- core\src\main\java\com\unipay\core\entity\PayOrder.java
- service\src\main\java\com\unipay\service\impl\RefundOrderService.java