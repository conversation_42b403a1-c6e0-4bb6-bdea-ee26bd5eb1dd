# UniPay Portal 部署指南

## 🚀 快速部署

### 1. 基础部署 (HTTP)

```bash
# 1. 上传文件到服务器
scp -r unipay-portal/ root@your-server:/tmp/

# 2. 登录服务器
ssh root@your-server

# 3. 进入项目目录
cd /tmp/unipay-portal

# 4. 给脚本执行权限
chmod +x *.sh

# 5. 运行部署脚本
sudo ./deploy.sh
```

### 2. SSL证书配置 (HTTPS)

```bash
# 确保域名已解析到服务器
# 运行SSL配置脚本
sudo ./ssl-setup.sh
```

### 3. 服务监控

```bash
# 检查服务状态
./monitor.sh

# 实时监控
./monitor.sh -w

# 生成监控报告
./monitor.sh -r
```

## 📋 部署前检查清单

### 服务器要求
- [ ] CentOS 7+ 或 Ubuntu 18.04+
- [ ] 至少 1GB RAM
- [ ] 至少 10GB 磁盘空间
- [ ] Root 权限

### 网络要求
- [ ] 域名 `ybdl.shop` 已解析到服务器IP
- [ ] 开放端口 80 (HTTP)
- [ ] 开放端口 443 (HTTPS)
- [ ] 服务器可访问互联网

### 后端服务
- [ ] 运营平台服务运行在端口 9217
- [ ] 代理商平台服务运行在端口 9219
- [ ] 商户平台服务运行在端口 9218
- [ ] 支付网关服务运行在端口 9216 (可选)

## 🔧 配置说明

### 域名配置
当前配置的域名是 `ybdl.shop`，如需修改：

1. 编辑 `nginx.conf` 文件中的 `server_name`
2. 编辑 `deploy.sh` 文件中的 `DOMAIN` 变量
3. 编辑 `ssl-setup.sh` 文件中的 `DOMAIN` 变量

### 路径配置
当前项目路径是 `/www/wwwroot/unipay-portal`，如需修改：

1. 编辑 `nginx.conf` 文件中的 `root` 路径
2. 编辑 `deploy.sh` 文件中的 `PROJECT_PATH` 变量

### 端口配置
如果后端服务端口有变化，需要修改 `nginx.conf` 中对应的 `proxy_pass` 配置。

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `deploy.sh` | 主部署脚本，自动安装和配置所有组件 |
| `ssl-setup.sh` | SSL证书配置脚本，使用Let's Encrypt |
| `monitor.sh` | 服务监控脚本，检查各服务状态 |
| `nginx.conf` | Nginx配置文件 |
| `index.html` | 门户首页 |
| `styles.css` | 样式文件 |
| `script.js` | JavaScript功能 |
| `404.html` | 404错误页面 |
| `50x.html` | 服务器错误页面 |

## 🔍 故障排除

### 常见问题

#### 1. 网站无法访问
```bash
# 检查nginx状态
systemctl status nginx

# 检查端口监听
netstat -tlnp | grep :80

# 检查防火墙
firewall-cmd --list-all  # CentOS
ufw status               # Ubuntu
```

#### 2. 后端服务无法访问
```bash
# 检查后端服务端口
netstat -tlnp | grep :9217
netstat -tlnp | grep :9218
netstat -tlnp | grep :9219

# 检查nginx错误日志
tail -f /var/log/nginx/unipay-portal.error.log
```

#### 3. SSL证书问题
```bash
# 检查证书文件
ls -la /etc/letsencrypt/live/ybdl.shop/

# 测试证书
openssl x509 -in /etc/letsencrypt/live/ybdl.shop/fullchain.pem -text -noout

# 手动续期证书
certbot renew --dry-run
```

### 日志位置
- Nginx访问日志: `/var/log/nginx/unipay-portal.access.log`
- Nginx错误日志: `/var/log/nginx/unipay-portal.error.log`
- 系统日志: `/var/log/messages` 或 `/var/log/syslog`

### 配置文件位置
- Nginx配置: `/etc/nginx/sites-available/unipay-portal`
- SSL证书: `/etc/letsencrypt/live/ybdl.shop/`
- 项目文件: `/www/wwwroot/unipay-portal/`

## 🔄 更新和维护

### 更新门户页面
```bash
# 1. 备份当前文件
cp -r /www/wwwroot/unipay-portal /tmp/unipay-portal-backup-$(date +%Y%m%d)

# 2. 上传新文件
scp new-files/* root@server:/www/wwwroot/unipay-portal/

# 3. 重启nginx
systemctl reload nginx
```

### 更新nginx配置
```bash
# 1. 备份配置
cp /etc/nginx/sites-available/unipay-portal /tmp/nginx.conf.bak

# 2. 更新配置
cp nginx.conf /etc/nginx/sites-available/unipay-portal

# 3. 测试配置
nginx -t

# 4. 重载配置
systemctl reload nginx
```

### 证书续期
证书会自动续期，也可以手动执行：
```bash
certbot renew
systemctl reload nginx
```

## 📊 监控和告警

### 使用监控脚本
```bash
# 基础检查
./monitor.sh

# 实时监控
./monitor.sh --watch

# 生成报告
./monitor.sh --report

# 仅检查服务
./monitor.sh --services

# 仅检查SSL
./monitor.sh --ssl
```

### 设置定时监控
```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次服务状态
*/5 * * * * /www/wwwroot/unipay-portal/monitor.sh -s >> /var/log/unipay-monitor.log 2>&1

# 每天生成监控报告
0 8 * * * /www/wwwroot/unipay-portal/monitor.sh -r
```

## 🔒 安全建议

### 基础安全
1. 定期更新系统和软件包
2. 配置防火墙规则
3. 使用强密码和密钥认证
4. 定期备份重要数据

### Nginx安全
1. 隐藏nginx版本信息
2. 配置访问限制
3. 启用访问日志监控
4. 定期检查配置文件

### SSL安全
1. 使用强加密套件
2. 启用HSTS
3. 定期检查证书有效期
4. 监控证书透明度日志

## 📞 技术支持

如遇到问题，请按以下步骤：

1. 查看本文档的故障排除部分
2. 运行监控脚本检查状态
3. 查看相关日志文件
4. 收集错误信息和系统环境
5. 联系技术支持

---

**部署完成后的访问地址：**
- 门户首页: https://ybdl.shop
- 运营平台: https://ybdl.shop/manager
- 代理商平台: https://ybdl.shop/agent
- 商户平台: https://ybdl.shop/merchant