
# 消息类型详解

<cite>
**本文档引用文件**   
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java)
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [CleanMchLoginAuthCacheMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/CleanMchLoginAuthCacheMQ.java)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java)
- [CleanMchLoginAuthCacheMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/CleanMchLoginAuthCacheMQReceiver.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
- [MchNotifyRecordService.java](file://service/src/main/java/com/unipay/service/impl/MchNotifyRecordService.java)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java)
</cite>

## 目录
1. [支付订单商户通知消息 (PayOrderMchNotifyMQ)](#支付订单商户通知消息-payordermchnotifymq)
2. [支付分账指令消息 (PayOrderDivisionMQ)](#支付分账指令消息-payorderdivisionmq)
3. [重置应用配置消息 (ResetAppConfigMQ)](#重置应用配置消息-resetappconfigmq)
4. [清理商户登录认证缓存消息 (CleanMchLoginAuthCacheMQ)](#清理商户登录认证缓存消息-cleanmchloginauthcachemq)

## 支付订单商户通知消息 (PayOrderMchNotifyMQ)

`PayOrderMchNotifyMQ` 消息用于在支付订单状态变更后，异步通知商户支付结果。该消息由支付网关发出，其接收器在支付网关服务中实现。

### 消息结构
该消息的载荷（`MsgPayload`）包含一个核心字段：
- **通知单号 (notifyId)**: `Long` 类型，表示商户通知记录表（`t_mch_notify_record`）中的主键ID。

### 业务场景与处理流程
1.  **消息接收**: `PayOrderMchNotifyMQReceiver` 接收消息。
2.  **状态校验**: 根据 `notifyId` 查询 `MchNotifyRecord` 记录，检查记录是否存在且状态为“通知中”（`STATE_ING`），并确认未达到最大通知次数。
3.  **首次通知标记**: 如果是第一次通知（`currentCount == 1`），则调用 `PayOrderService.updateNotifySent()` 方法，将支付订单的 `notifyState` 更新为“已发送”。
4.  **HTTP回调**: 向商户配置的 `notifyUrl` 发起HTTP POST请求，发送支付结果。
5.  **结果处理**:
    -   **成功响应**: 若回调返回“SUCCESS”，则调用 `MchNotifyRecordService.updateNotifyResult()` 将通知记录状态更新为“成功”。
    -   **失败或未达上限**: 若回调失败但未达到最大通知次数，则更新状态为“通知中”，并通过消息队列延迟重发（延迟时间随次数递增：0s, 30s, 60s...）。
    -   **达到上限**: 若已达到最大通知次数，则将通知记录状态更新为“失败”。

### 幂等性保证机制
该消息的幂等性主要通过数据库状态机来保证：
-   每次处理前都会检查 `MchNotifyRecord` 的状态，只有状态为“通知中”才会继续处理。
-   通知次数的更新和状态变更在同一个事务中完成，防止重复处理。
-   消息的延迟重发是基于业务逻辑（未成功且未达上限）触发的，而非消息队列本身的重试机制。

### 错误处理策略
-   **网络错误**: 捕获HTTP请求异常，记录错误日志，并将错误信息作为响应结果存储。
-   **业务逻辑错误**: 捕获 `BizException` 等业务异常，记录日志并终止当前处理流程。
-   **重试机制**: 对于非最终失败的情况，采用指数退避的延迟重发策略，避免对商户服务器造成过大压力。
-   **最终失败**: 当重试次数耗尽后，将通知状态标记为“失败”，并记录最终的错误响应。

**Section sources**
- [PayOrderMchNotifyMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderMchNotifyMQ.java#L16-L68)
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderMchNotifyMQReceiver.java#L23-L98)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L193-L198)
- [MchNotifyRecordService.java](file://service/src/main/java/com/unipay/service/impl/MchNotifyRecordService.java#L41-L43)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
- [MchNotifyRecord.java](file://core/src/main/java/com/unipay/core/entity/MchNotifyRecord.java#L24-L140)

## 支付分账指令消息 (PayOrderDivisionMQ)

`PayOrderDivisionMQ` 消息用于触发支付订单的分账流程。该消息由支付网关发出，分账服务接收并处理。

### 消息结构
该消息的载荷（`MsgPayload`）包含以下核心字段：
- **支付订单号 (payOrderId)**: `String` 类型，系统生成的唯一订单号。
- **是否使用默认分组 (useSysAutoDivisionReceivers)**: `Byte` 类型，标识是否使用系统配置的默认分账接收者。
- **分账接受者列表 (receiverList)**: `List<CustomerDivisionReceiver>` 类型，包含自定义的分账接收者信息。每个接收者包含 `receiverId`、`receiverGroupId` 和 `divisionProfit`（分账比例）。
- **是否重新发送 (isResend)**: `Boolean` 类型，标识是否为重新发起的分账请求。

### 业务场景与处理流程
1.  **消息接收**: `PayOrderDivisionMQReceiver` 接收消息。
2.  **订单校验**: 调用 `PayOrderService` 查询支付订单，确保订单存在且分账状态为“等待分账”或“未发生分账”。
3.  **状态更新**: 将支付订单的 `divisionState` 原子性地更新为“分账处理中”（`DIVISION_STATE_ING`），防止并发处理。
4.  **分账记录生成**:
    -   若为重发请求（`isResend=true`），则从数据库查询待分账的记录。
    -   否则，根据配置（默认或自定义）计算所有分账接收者的分账金额，并生成 `PayOrderDivisionRecord` 记录。
5.  **调用渠道分账接口**: 通过Spring Bean工厂获取对应的 `IDivisionService` 实现，调用 `singleDivision` 方法向支付渠道发起分账请求。
6.  **结果处理**: 根据渠道返回的 `ChannelRetMsg` 状态，批量更新 `PayOrderDivisionRecord` 的状态（成功、失败、已受理）。
7.  **订单状态更新**: 无论分账成功与否，都将支付订单的 `divisionState` 更新为“分账任务已结束”（`DIVISION_STATE_FINISH`）。

### 幂等性保证机制
-   **数据库乐观锁**: 在更新支付订单状态时，使用MyBatis-Plus的 `LambdaUpdateWrapper` 指定 `eq(PayOrder::getDivisionState, payOrder.getDivisionState)`，确保只有当订单状态未被其他线程修改时，更新才能成功。
-   **状态机控制**: 整个分账流程依赖于 `PayOrder` 和 `PayOrderDivisionRecord` 的状态字段，确保流程按预设顺序执行，避免重复操作。

### 错误处理策略
-   **业务异常**: 捕获 `BizException` 并记录日志，终止流程。
-   **系统异常**: 在 `try-catch` 块中捕获所有异常，确保即使调用渠道接口失败，也能将分账记录标记为“失败”，并将支付订单状态更新为“结束”，防止订单卡在“处理中”状态。
-   **渠道返回**: 根据渠道返回的明确状态（成功、失败、已受理）进行精确的状态更新。

**Section sources**
- [PayOrderDivisionMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderDivisionMQ.java#L19-L114)
- [PayOrderDivisionMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderDivisionMQReceiver.java#L15-L33)
- [PayOrderDivisionProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderDivisionProcessService.java#L58-L188)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)
- [PayOrderDivisionRecordService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderDivisionRecordService.java#L24-L88)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java#L24-L278)
- [PayOrderDivisionRecord.java](file://core/src/main/java/com/unipay/core/entity/PayOrderDivisionRecord.java#L23-L205)

## 重置应用配置消息 (ResetAppConfigMQ)

`ResetAppConfigMQ` 消息用于在系统配置更新后，广播通知所有服务实例同步最新的配置。这是一种广播模式（`BROADCAST`）的消息。

### 消息结构
该消息的载荷（`MsgPayload`）包含一个核心字段：
- **分组key (groupKey)**: `String` 类型，标识需要刷新的配置分组，例如 `applicationConfig`。

### 业务场景与处理流程
1.  **消息广播**: 当系统配置（`t_sys_config` 表）被修改后，系统会发送 `ResetAppConfigMQ` 消息。
2.  **多实例接收**: 所有运行中的服务实例（如 `sys-manager`, `sys-merchant`, `sys-payment`, `sys-agent`）都会接收到该消息，因为它们都实现了 `ResetAppConfigMQ.IMQReceiver` 接口。
3.  **配置刷新**: 每个实例的 `ResetAppConfigMQReceiver` 接收器会调用 `SysConfigService.initDBConfig(groupKey)` 方法。
4.  **缓存更新**: `initDBConfig` 方法会根据 `groupKey` 从数据库重新查询配置，并更新到内存缓存（`APPLICATION_CONFIG`）中，从而实现所有实例的配置同步。

### 幂等性保证机制
该消息的幂等性要求不高，因为其操作是“刷新缓存”这一幂等操作。
-   重复接收消息会导致重复的数据库查询和缓存更新，但这不会产生副作用，只会带来轻微的性能开销。
-   代码中通过 `synchronized` 关键字修饰 `initDBConfig` 方法，确保同一实例内对缓存的更新是线程安全的。

### 错误处理策略
-   **日志记录**: 记录接收消息和刷新配置的日志，便于追踪。
-   **异常捕获**: 捕获并记录执行过程中的异常，但通常不会中断流程，因为配置刷新失败可能导致实例使用旧配置，但服务本身仍可运行。

**Section sources**
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)
- [SysConfig.java](file://core/src/main/java/com/unipay/core/entity/SysConfig.java#L24-L94)

## 清理商户登录认证缓存消息 (CleanMchLoginAuthCacheMQ)

`CleanMchLoginAuthCacheMQ` 消息用于在商户信息变更（如密码修改、权限调整）时，清理相关用户的登录认证缓存，强制其重新登录。

### 消息结构
该消息的载荷（`MsgPayload`）包含一个核心字段：
- **用户ID集合 (userIdList)**: `List<Long>` 类型，表示需要清理缓存的系统用户ID列表。

### 业务场景与处理流程
1.  **消息接收**: `CleanMchLoginAuthCacheMQReceiver` 接收消息。
2.  **缓存键查询**: 遍历 `userIdList`，使用 `CS.getCacheKeyToken(sysUserId, "*")` 构造Redis键的模糊匹配模式（如 `TOKEN:1001:*`）。
3.  **缓存清理**: 调用 `RedisUtil.keys()` 查询所有匹配的键，然后通过 `RedisUtil.del()` 批量删除这些键，从而清除用户的登录Token和认证信息。

### 幂等性保证机制
Redis的 `DEL` 命令是幂等的。即使同一个用户ID被多次清理，或者键已被删除，再次执行 `DEL` 操作也不会产生错误或副作用。

### 错误处理策略
-   **空值检查**: 检查 `userIdList` 是否为空