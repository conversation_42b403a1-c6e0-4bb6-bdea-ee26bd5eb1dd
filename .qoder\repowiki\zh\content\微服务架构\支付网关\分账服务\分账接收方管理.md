# 分账接收方管理

<cite>
**本文档引用的文件**  
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java)
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java)
- [DivisionReceiverPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\DivisionReceiverPage.vue)
- [ReceiverAdd.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\ReceiverAdd.vue)
- [DivisionReceiverGroupPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\group\DivisionReceiverGroupPage.vue)
</cite>

## 目录
1. [系统概述](#系统概述)
2. [核心实体说明](#核心实体说明)
3. [分账接收方管理](#分账接收方管理)
4. [分账接收方分组管理](#分账接收方分组管理)
5. [前端交互流程](#前端交互流程)
6. [安全与校验机制](#安全与校验机制)

## 系统概述

分账接收方管理是UniPay支付系统中的核心功能模块，主要用于管理商户的分账规则和接收方信息。系统通过`MchDivisionReceiverController`和`MchDivisionReceiverGroupController`两个控制器分别管理分账接收方及其分组。该功能允许商户将交易金额按预设比例分配给多个接收方，实现灵活的资金分配策略。

代理商系统和商户系统均提供了对分账接收方的完整管理能力，包括增删改查、分组管理、权限控制等功能。前端通过`unipay-web-ui`项目中的商户系统界面与后端API进行交互，实现了直观易用的操作界面。

**Section sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)

## 核心实体说明

### MchDivisionReceiver 实体

`MchDivisionReceiver`实体用于表示分账接收方的具体信息，包含以下关键字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| receiverId | Long | 分账接收者ID，主键 |
| receiverAlias | String | 接收者账号别名 |
| receiverGroupId | Long | 所属分组ID |
| receiverGroupName | String | 所属分组名称 |
| mchNo | String | 商户号 |
| appId | String | 应用ID |
| ifCode | String | 支付接口代码（如wxpay、alipay） |
| accType | Byte | 账号类型：0-个人(对私)，1-商户(对公) |
| accNo | String | 接收方账号 |
| accName | String | 接收方姓名 |
| relationType | String | 分账关系类型（参考微信） |
| relationTypeName | String | 自定义关系类型名称 |
| divisionProfit | BigDecimal | 分账比例（小数形式） |
| state | Byte | 分账状态：1-正常分账，0-暂停分账 |

**Section sources**
- [MchDivisionReceiver.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiver.java)

### MchDivisionReceiverGroup 实体

`MchDivisionReceiverGroup`实体用于表示分账接收方的分组信息，实现可复用的分账模板，包含以下关键字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| receiverGroupId | Long | 组ID，主键 |
| receiverGroupName | String | 组名称 |
| mchNo | String | 商户号 |
| autoDivisionFlag | Byte | 自动分账组标志：0-否，1-是 |
| createdUid | Long | 创建者用户ID |
| createdBy | String | 创建者姓名 |
| createdAt | Date | 创建时间 |

**Section sources**
- [MchDivisionReceiverGroup.java](file://core\src\main\java\com\unipay\core\entity\MchDivisionReceiverGroup.java)

## 分账接收方管理

### 功能接口

`MchDivisionReceiverController`提供了对分账接收方的完整CRUD操作：

```mermaid
flowchart TD
A[分账接收方管理] --> B[列表查询]
A --> C[详情查看]
A --> D[新增接收方]
A --> E[更新接收方]
A --> F[删除接收方]
B --> B1["GET /api/divisionReceivers"]
C --> C1["GET /api/divisionReceivers/{recordId}"]
D --> D1["POST /api/divisionReceivers"]
E --> E1["PUT /api/divisionReceivers/{recordId}"]
F --> F1["DELETE /api/divisionReceivers/{recordId}"]
```

**Diagram sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)

### 增删改查流程

1. **列表查询**：支持按应用ID、接收方ID、别名、状态、分组等条件进行分页查询
2. **详情查看**：根据接收方ID获取具体信息
3. **新增接收方**：创建新的分账接收方，需关联应用和分组
4. **更新接收方**：修改接收方信息，包括别名、分组、分账比例等
5. **删除接收方**：移除指定的分账接收方

所有操作均需通过权限验证，确保只有授权用户才能执行相应操作。

**Section sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)

## 分账接收方分组管理

### 功能接口

`MchDivisionReceiverGroupController`提供了对分账接收方分组的管理功能：

```mermaid
flowchart TD
G[分账接收方分组管理] --> H[列表查询]
G --> I[详情查看]
G --> J[新增分组]
G --> K[更新分组]
G --> L[删除分组]
H --> H1["GET /api/divisionReceiverGroups"]
I --> I1["GET /api/divisionReceiverGroups/{recordId}"]
J --> J1["POST /api/divisionReceiverGroups"]
K --> K1["PUT /api/divisionReceiverGroups/{recordId}"]
L --> L1["DELETE /api/divisionReceiverGroups/{recordId}"]
```

**Diagram sources**
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)

### 分组管理特性

1. **自动分账组**：可设置一个分组为自动分账组，当订单分账模式为自动分账时，该组将自动完成分账逻辑
2. **唯一性约束**：系统确保同一商户下只有一个自动分账组，当设置新的自动分账组时，原有的自动分账组将被自动取消
3. **删除限制**：只有当分组内没有关联的接收方时，才能删除该分组，防止数据不一致

分组功能使得商户可以创建可复用的分账模板，提高分账配置的效率和一致性。

**Section sources**
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)

## 前端交互流程

### 前端组件结构

```mermaid
graph TD
A[商户系统前端] --> B[分账管理模块]
B --> C[接收方管理页面]
B --> D[分组管理页面]
C --> C1[接收方列表]
C --> C2[新增接收方抽屉]
C --> C3[修改接收方抽屉]
D --> D1[分组列表]
D --> D2[新增/修改分组弹窗]
```

**Diagram sources**
- [DivisionReceiverPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\DivisionReceiverPage.vue)
- [DivisionReceiverGroupPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\group\DivisionReceiverGroupPage.vue)

### 接收方管理界面

前端通过`DivisionReceiverPage.vue`组件实现接收方的管理界面，主要功能包括：

1. **搜索过滤**：支持按应用、接收方ID、别名、状态、分组等条件进行搜索
2. **状态显示**：使用不同颜色的标签显示分账状态（正常分账、暂停分账）
3. **支付渠道标识**：使用图标和颜色区分微信支付和支付宝等不同支付渠道
4. **操作按钮**：提供新增、修改等操作按钮，根据用户权限动态显示

```mermaid
flowchart TD
Start[用户进入分账接收方管理页面] --> Search[输入搜索条件]
Search --> Query[点击查询按钮]
Query --> API[调用后端API获取数据]
API --> Display[在表格中显示接收方列表]
Display --> Action[选择操作]
Action --> Add[点击新增]
Action --> Edit[点击修改]
Action --> Delete[点击删除]
Add --> AddForm[打开新增抽屉]
Edit --> EditForm[打开修改抽屉]
Delete --> Confirm[确认删除]
AddForm --> Validate[前端表单验证]
EditForm --> Validate
Validate --> Submit[提交表单]
Submit --> Backend[调用后端API]
Backend --> Result[显示操作结果]
```

**Diagram sources**
- [DivisionReceiverPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\DivisionReceiverPage.vue)
- [ReceiverAdd.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\ReceiverAdd.vue)

### 新增接收方流程

新增接收方时，前端通过`ReceiverAdd.vue`组件实现批量绑定功能：

1. 用户选择应用，系统自动加载该应用支持的支付方式
2. 用户选择分组，系统显示该分组信息
3. 用户填写接收方信息，包括姓名、账号、分账比例等
4. 系统对输入数据进行前端验证
5. 用户点击"发起绑定请求"，系统逐个调用后端API完成绑定

**Section sources**
- [DivisionReceiverPage.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\DivisionReceiverPage.vue)
- [ReceiverAdd.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\ReceiverAdd.vue)

## 安全与校验机制

### 权限控制

系统通过Spring Security的`@PreAuthorize`注解实现细粒度的权限控制：

| 操作 | 所需权限 |
|------|----------|
| 接收方列表 | ENT_DIVISION_RECEIVER_LIST |
| 接收方详情 | ENT_DIVISION_RECEIVER_VIEW |
| 新增接收方 | ENT_DIVISION_RECEIVER_ADD |
| 修改接收方 | ENT_DIVISION_RECEIVER_EDIT |
| 删除接收方 | ENT_DIVISION_RECEIVER_DELETE |
| 分组列表 | ENT_DIVISION_RECEIVER_GROUP_LIST |
| 分组详情 | ENT_DIVISION_RECEIVER_GROUP_VIEW |
| 新增分组 | ENT_DIVISION_RECEIVER_GROUP_ADD |
| 修改分组 | ENT_DIVISION_RECEIVER_GROUP_EDIT |
| 删除分组 | ENT_DIVISION_RECEIVER_GROUP_DELETE |

**Section sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)

### 数据校验

系统在多个层面实施数据校验：

1. **前端校验**：
   - 接收方姓名、账号、分账比例等必填字段验证
   - 分账比例范围验证（0.01% ~ 100%）
   - 自定义关系类型时名称不能为空

2. **后端校验**：
   - 商户应用存在性和可用性验证
   - 商户号与当前登录商户匹配验证
   - 分组存在性验证
   - 删除分组时检查是否有关联的接收方

3. **业务逻辑校验**：
   - 自动分账组的唯一性保证
   - 分账比例的数值范围和精度控制
   - 敏感操作的日志记录

这些安全措施确保了分账接收方管理功能的稳定性和安全性，防止非法操作和数据不一致。

**Section sources**
- [MchDivisionReceiverController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverController.java)
- [MchDivisionReceiverGroupController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\division\MchDivisionReceiverGroupController.java)
- [ReceiverAdd.vue](file://unipay-web-ui\unipay-ui-merchant\src\views\division\receiver\ReceiverAdd.vue)