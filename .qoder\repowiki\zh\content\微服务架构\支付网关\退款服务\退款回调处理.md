# 退款回调处理

<cite>
**本文档引用文件**   
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java)
- [PayOrderReissueMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderReissueMQReceiver.java)
- [RefundOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/RefundOrderReissueTask.java)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java)
- [IChannelRefundNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelRefundNoticeService.java)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [处理流程](#处理流程)
4. [安全防护与日志记录](#安全防护与日志记录)
5. [重试机制](#重试机制)
6. [结论](#结论)

## 简介
本文档详细阐述了统一支付系统中退款回调处理的完整流程。重点描述了`ChannelRefundNoticeController`如何接收来自支付宝、微信等支付渠道的异步退款通知，以及`RefundOrderProcessService`如何处理这些通知并更新本地订单状态。同时，文档还解释了在处理失败时通过消息队列（如`PayOrderReissueMQ`）触发的重试机制，以及相关的安全防护措施和日志记录最佳实践。

## 核心组件

本节分析处理退款回调的核心类及其职责。

**Section sources**
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java#L39-L124)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java#L23-L45)
- [IChannelRefundNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelRefundNoticeService.java)

### ChannelRefundNoticeController
`ChannelRefundNoticeController`是处理渠道侧退款异步通知的入口控制器。它负责接收HTTP请求，协调各个服务完成整个回调处理流程。

```mermaid
classDiagram
class ChannelRefundNoticeController {
+refundOrderService RefundOrderService
+configContextQueryService ConfigContextQueryService
+refundOrderProcessService RefundOrderProcessService
+doNotify(request, ifCode, urlOrderId) ResponseEntity
}
class RefundOrderService {
+getById(refundOrderId) RefundOrder
+updateIng2Success(refundOrderId, channelOrderNo) boolean
+updateIng2Fail(refundOrderId, channelOrderNo, errCode, errMsg) boolean
}
class ConfigContextQueryService {
+queryMchInfoAndAppInfo(mchNo, appId) MchAppConfigContext
}
class RefundOrderProcessService {
+handleRefundOrder4Channel(channelRetMsg, refundOrder) boolean
}
class IChannelRefundNoticeService {
+parseParams(request, urlOrderId, noticeType) MutablePair~String, Object~
+doNotice(request, params, refundOrder, mchAppConfigContext, noticeType) ChannelRetMsg
}
ChannelRefundNoticeController --> RefundOrderService : "使用"
ChannelRefundNoticeController --> ConfigContextQueryService : "使用"
ChannelRefundNoticeController --> RefundOrderProcessService : "使用"
ChannelRefundNoticeController --> IChannelRefundNoticeService : "通过SpringBeansUtil获取"
```

**Diagram sources**
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java#L34-L36)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [ConfigContextQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ConfigContextQueryService.java)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java)
- [IChannelRefundNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelRefundNoticeService.java)

### RefundOrderProcessService
`RefundOrderProcessService`是处理退款订单业务逻辑的核心服务。它根据渠道返回的状态来更新本地退款订单的状态，并触发后续的商户通知。

```mermaid
classDiagram
class RefundOrderProcessService {
+refundOrderService RefundOrderService
+payMchNotifyService PayMchNotifyService
+handleRefundOrder4Channel(channelRetMsg, refundOrder) boolean
}
class RefundOrderService {
+updateIng2Success(refundOrderId, channelOrderNo) boolean
+updateIng2Fail(refundOrderId, channelOrderNo, errCode, errMsg) boolean
}
class PayMchNotifyService {
+refundOrderNotify(refundOrder) void
}
class ChannelRetMsg {
+channelState ChannelState
+channelOrderId String
+channelErrCode String
+channelErrMsg String
+responseEntity ResponseEntity
}
class RefundOrder {
+refundOrderId String
+notifyUrl String
+mchNo String
+appId String
}
RefundOrderProcessService --> RefundOrderService : "使用"
RefundOrderProcessService --> PayMchNotifyService : "使用"
```

**Diagram sources**
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java#L19-L20)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)

## 处理流程

本节详细描述从接收到渠道通知到最终更新订单状态的完整流程。

```mermaid
sequenceDiagram
participant 渠道 as 支付渠道(支付宝/微信)
participant Controller as ChannelRefundNoticeController
participant Service as RefundOrderProcessService
participant DB as 数据库
participant Merchant as 商户系统
渠道->>Controller : 发送退款回调请求
activate Controller
Controller->>Controller : 验证ifCode参数
Controller->>Controller : 通过SpringBeansUtil获取IChannelRefundNoticeService
Controller->>IChannelRefundNoticeService : 调用parseParams解析请求
IChannelRefundNoticeService-->>Controller : 返回订单号和参数
Controller->>DB : 查询RefundOrder
DB-->>Controller : 返回订单数据
Controller->>ConfigContextQueryService : 查询商户应用配置
ConfigContextQueryService-->>Controller : 返回MchAppConfigContext
Controller->>IChannelRefundNoticeService : 调用doNotice处理通知
IChannelRefundNoticeService-->>Controller : 返回ChannelRetMsg
Controller->>Service : 调用handleRefundOrder4Channel
activate Service
Service->>RefundOrderService : 根据状态更新订单
RefundOrderService-->>Service : 返回更新结果
alt 更新成功
Service->>PayMchNotifyService : 触发商户通知
PayMchNotifyService-->>Service : 通知成功
Service-->>Controller : 返回true
Controller-->>渠道 : 返回成功响应
else 更新失败
Service-->>Controller : 返回false
Controller->>IChannelRefundNoticeService : 调用doNotifyOrderStateUpdateFail
IChannelRefundNoticeService-->>Controller : 返回失败响应
Controller-->>渠道 : 返回失败响应
end
deactivate Service
deactivate Controller
```

**Diagram sources**
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java#L39-L124)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java#L23-L45)
- [IChannelRefundNoticeService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IChannelRefundNoticeService.java)

**Section sources**
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java#L39-L124)
- [RefundOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/RefundOrderProcessService.java#L23-L45)

### 通知接收与验签
`ChannelRefundNoticeController`通过`doNotify`方法接收来自支付渠道的异步通知。该方法首先验证`ifCode`参数，然后通过`SpringBeansUtil.getBean`动态获取对应支付渠道的`IChannelRefundNoticeService`实现。验签和数据解析的具体逻辑由具体的渠道实现类（如`WxpayChannelRefundNoticeService`）在`parseParams`和`doNotice`方法中完成。

### 数据解析与状态更新
在成功解析请求参数并获取到`RefundOrder`对象后，控制器会调用`RefundOrderProcessService`的`handleRefundOrder4Channel`方法。该方法根据`ChannelRetMsg`中的`channelState`来决定如何更新订单状态：
- 当`channelState`为`CONFIRM_SUCCESS`时，调用`RefundOrderService.updateIng2Success`将订单状态更新为“退款成功”，并触发向商户系统的通知。
- 当`channelState`为`CONFIRM_FAIL`时，调用`RefundOrderService.updateIng2Fail`将订单状态更新为“退款失败”，同样触发向商户系统的通知。

## 安全防护与日志记录

系统在处理退款回调时实施了多项安全防护措施，并通过详细的日志记录来保障可追溯性。

### 安全防护
- **参数校验**：在处理开始时即对`ifCode`等关键参数进行非空校验。
- **订单号匹配**：如果URL中包含`refundOrderId`，会与解析出的订单号进行比对，防止订单号被篡改。
- **接口存在性验证**：通过`SpringBeansUtil.getBean`检查指定的支付渠道服务是否存在，防止非法调用。
- **幂等性保证**：`RefundOrderService`中的`updateIng2Success`和`updateIng2Fail`方法在更新数据库时使用了条件更新（`eq(RefundOrder::getState, RefundOrder.STATE_ING)`），确保只有在订单处于“退款中”状态时才能被更新，防止重复处理。

### 日志记录
系统在关键节点都记录了详细的日志信息，便于问题排查和审计：
- 在进入回调方法时记录`logPrefix`，包含`ifCode`和`urlOrderId`，方便追踪。
- 记录参数解析的结果，包括解析出的`refundOrderId`和请求参数。
- 在订单不存在或更新失败时记录详细的错误日志。
- 在处理成功后记录“订单通知完成”的信息。

**Section sources**
- [ChannelRefundNoticeController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/refund/ChannelRefundNoticeController.java#L39-L124)
- [RefundOrderService.java](file://service/src/main/java/com/unipay/service/impl/RefundOrderService.java)

## 重试机制

当退款回调处理失败或未收到渠道回调时，系统通过定时任务和消息队列（MQ）来触发重试，确保订单状态最终一致。

```mermaid
flowchart TD
A[RefundOrderReissueTask] --> |每分钟执行一次| B{查询数据库}
B --> |状态为退款中的订单| C[ChannelOrderReissueService]
C --> D{processRefundOrder}
D --> |调用渠道查询接口| E[渠道服务]
E --> |返回结果| F{ChannelRetMsg}
F --> |成功/失败| G[RefundOrderProcessService]
G --> |更新订单状态| H[数据库]
F --> |等待中| I[PayOrderReissueMQ]
I --> |发送消息| J[PayOrderReissueMQReceiver]
J --> |延迟5秒| K[再次调用processRefundOrder]
K --> D
```

**Diagram sources**
- [RefundOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/RefundOrderReissueTask.java#L27-L60)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java#L81-L116)
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java)
- [PayOrderReissueMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderReissueMQReceiver.java#L31-L70)

**Section sources**
- [RefundOrderReissueTask.java](file://sys-payment/src/main/java/com/unipay/pay/task/RefundOrderReissueTask.java#L27-L60)
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java#L81-L116)
- [PayOrderReissueMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/PayOrderReissueMQ.java)
- [PayOrderReissueMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/PayOrderReissueMQReceiver.java#L31-L70)

### 定时任务
`RefundOrderReissueTask`是一个定时任务，每分钟执行一次。它会查询所有状态为“退款中”（`STATE_ING`）的退款订单，并将它们交给`ChannelOrderReissueService`进行处理。

### 补单服务
`ChannelOrderReissueService`的`processRefundOrder`方法会调用对应支付渠道的查询接口，获取最新的订单状态。如果查询结果为“等待中”（`WAITING`），则会通过`PayOrderReissueMQ`发送一条延迟消息，5秒后再次尝试查询。最多尝试6次，以避免无限循环。

### 消息队列
`PayOrderReissueMQ`定义了补单消息的格式，包含`payOrderId`和`count`（重试次数）。`PayOrderReissueMQReceiver`作为消息接收者，负责接收并处理这些消息，驱动重试流程。

## 结论
统一支付系统的退款回调处理机制设计严谨，流程清晰。通过`ChannelRefundNoticeController`作为统一入口，结合`RefundOrderProcessService`进行业务处理，实现了对支付宝、微信等不同渠道通知的统一管理。系统通过详细的日志记录和多重安全校验保障了处理过程的可靠性和安全性。此外，基于定时任务和消息队列的重试机制有效解决了因网络问题导致的回调丢失，确保了订单状态的最终一致性。该设计模式具有良好的扩展性和维护性，为系统的稳定运行提供了坚实的基础。