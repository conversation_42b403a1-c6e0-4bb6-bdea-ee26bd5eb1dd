# UniPay 统一支付平台 Nginx 配置
# 建议放置在 /etc/nginx/sites-available/ 或 /etc/nginx/conf.d/

# 上游服务器配置
upstream unipay_manager {
    server 127.0.0.1:8080;
    # 如果有多个实例，可以添加负载均衡
    # server 127.0.0.1:8081;
}

upstream unipay_payment {
    server 127.0.0.1:9216;
}

upstream unipay_merchant {
    server 127.0.0.1:9218;
}

upstream unipay_agent {
    server 127.0.0.1:9219;
}

upstream unipay_game {
    server 127.0.0.1:8088;
}

# 管理后台 - manager.unipay.com
server {
    listen 80;
    server_name manager.unipay.com;
    
    # 重定向到HTTPS (生产环境推荐)
    # return 301 https://$server_name$request_uri;
    
    # 日志配置
    access_log /var/log/nginx/unipay-manager-access.log;
    error_log /var/log/nginx/unipay-manager-error.log;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    # API接口代理
    location /api/ {
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # WebSocket支持 (如果需要)
    location /ws/ {
        proxy_pass http://unipay_manager;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Swagger文档 (开发环境)
    location /swagger-ui.html {
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /v3/api-docs {
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 默认路由到后端
    location / {
        proxy_pass http://unipay_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 商户平台 - merchant.unipay.com
server {
    listen 80;
    server_name merchant.unipay.com;
    
    access_log /var/log/nginx/unipay-merchant-access.log;
    error_log /var/log/nginx/unipay-merchant-error.log;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        proxy_pass http://unipay_merchant;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# 代理商平台 - agent.unipay.com
server {
    listen 80;
    server_name agent.unipay.com;
    
    access_log /var/log/nginx/unipay-agent-access.log;
    error_log /var/log/nginx/unipay-agent-error.log;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        proxy_pass http://unipay_agent;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# 支付网关 - pay.unipay.com
server {
    listen 80;
    server_name pay.unipay.com;
    
    access_log /var/log/nginx/unipay-payment-access.log;
    error_log /var/log/nginx/unipay-payment-error.log;
    
    # 支付接口限流 (防止恶意请求)
    limit_req_zone $binary_remote_addr zone=payment:10m rate=10r/s;
    
    location / {
        # 应用限流
        limit_req zone=payment burst=20 nodelay;
        
        proxy_pass http://unipay_payment;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 支付接口超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# 游戏服务 - game.unipay.com
server {
    listen 80;
    server_name game.unipay.com;
    
    access_log /var/log/nginx/unipay-game-access.log;
    error_log /var/log/nginx/unipay-game-error.log;
    
    location / {
        proxy_pass http://unipay_game;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}

# HTTPS配置示例 (生产环境推荐)
# server {
#     listen 443 ssl http2;
#     server_name manager.unipay.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置同HTTP版本
# }