# 支付查询

<cite>
**本文档引用文件**   
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java)
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java)
- [IPayOrderQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderQueryService.java)
- [PayOrder.java](file://core/src/main/java/com/unipay/core/entity/PayOrder.java)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java)
</cite>

## 目录
1. [介绍](#介绍)
2. [请求参数设计与使用](#请求参数设计与使用)
3. [响应对象字段说明](#响应对象字段说明)
4. [支付订单查询服务协调机制](#支付订单查询服务协调机制)
5. [订单状态同步机制](#订单状态同步机制)
6. [查询频率限制与缓存策略](#查询频率限制与缓存策略)
7. [处理查询结果不一致的代码示例](#处理查询结果不一致的代码示例)

## 介绍
支付查询功能是统一支付系统中的核心模块之一，用于商户通过商户订单号或平台订单号查询支付订单的详细状态信息。该功能支持多支付渠道的集成，能够准确同步本地订单状态与渠道订单状态，并提供完善的异常处理和通知机制。本文档详细说明了支付查询功能的设计原则、实现机制和使用方法。

## 请求参数设计与使用

支付查询请求参数 `QueryPayOrderRQ` 类继承自 `AbstractMchAppRQ`，包含商户号、应用ID以及用于查询的订单标识信息。设计原则遵循最小必要原则，仅包含完成查询操作所必需的参数。

`QueryPayOrderRQ` 类包含以下两个核心查询条件字段：

- **商户订单号 (mchOrderNo)**：商户系统生成的唯一订单编号，用于标识一笔交易。此字段由商户在发起支付请求时提供，是商户侧追踪订单的主要依据。
- **支付系统订单号 (payOrderId)**：统一支付平台生成的全局唯一订单编号，用于在平台内部唯一标识一笔支付订单。

这两个查询条件为互斥可选关系，即请求时必须至少提供其中一个。系统通过 `StringUtils.isAllEmpty(rq.getMchOrderNo(), rq.getPayOrderId())` 进行校验，若两者均为空，则抛出业务异常 "mchOrderNo 和 payOrderId不能同时为空"。

该设计允许商户根据自身业务场景灵活选择查询方式。例如，在商户系统中，通常使用 `mchOrderNo` 进行查询；而在平台运营后台，可能更倾向于使用全局唯一的 `payOrderId` 进行精确查询。

**Section sources**
- [QueryPayOrderRQ.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRQ.java#L11-L20)
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L31-L48)

## 响应对象字段说明

`QueryPayOrderRS` 响应对象继承自 `AbstractRS`，封装了支付订单的完整信息，其各字段的业务含义如下：

- **支付订单号 (payOrderId)**：统一支付平台生成的订单唯一标识。
- **商户号 (mchNo)**：发起支付请求的商户在平台中的唯一编号。
- **商户应用ID (appId)**：商户应用的唯一标识，用于区分同一商户下的不同应用。
- **商户订单号 (mchOrderNo)**：商户系统提供的订单号。
- **支付接口代码 (ifCode)**：标识所使用的具体支付渠道（如 ALIPAY, WXPAY）。
- **支付方式代码 (wayCode)**：标识具体的支付方式（如 JSAPI, NATIVE）。
- **支付金额 (amount)**：订单的支付金额，单位为分。
- **货币代码 (currency)**：交易使用的货币类型，如人民币为 "cny"。
- **支付状态 (state)**：订单的当前状态，枚举值包括：0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭。
- **客户端IP (clientIp)**：发起支付请求的客户端IP地址。
- **商品标题 (subject)** 和 **商品描述信息 (body)**：交易的商品信息。
- **渠道订单号 (channelOrderNo)**：支付渠道（如微信、支付宝）生成的订单号。
- **渠道支付错误码 (errCode)** 和 **渠道支付错误描述 (errMsg)**：当支付失败时，由渠道返回的错误信息。
- **商户扩展参数 (extParam)**：商户在支付请求时传入的扩展信息，原样返回。
- **订单支付成功时间 (successTime)**：订单支付成功的时间戳。
- **创建时间 (createdAt)**：订单在平台创建的时间戳。

该响应对象通过静态工厂方法 `buildByPayOrder(PayOrder payOrder)` 构建，该方法使用 `BeanUtils.copyProperties` 将 `PayOrder` 实体的属性复制到 `QueryPayOrderRS` 对象中，并将 `Date` 类型的 `successTime` 和 `createdAt` 转换为 `Long` 类型的时间戳。

**Section sources**
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java#L12-L121)
- [QueryPayOrderRS.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/payorder/QueryPayOrderRS.java#L106-L118)

## 支付订单查询服务协调机制

`PayOrderProcessService` 是支付订单处理的核心服务类，负责协调不同支付渠道的查询操作。其主要通过依赖注入的 `PayOrderService` 和 `IMQSender` 来实现业务逻辑。

当需要查询订单状态时，系统首先通过 `PayOrderService` 根据商户号、平台订单号或商户订单号从数据库中获取 `PayOrder` 实体。然后，根据 `PayOrder` 中的 `ifCode`（支付接口代码），利用 Spring 的依赖注入机制，通过 `SpringBeansUtil.getBean()` 动态获取对应的 `IPayOrderQueryService` 实现。

`IPayOrderQueryService` 是一个定义了查询行为的接口，所有支持查询的支付渠道都必须实现此接口。其核心方法 `query(PayOrder payOrder, MchAppConfigContext mchAppConfigContext)` 接收订单信息和商户配置上下文，向具体的支付渠道发起查询请求。

例如，`AlipayPayOrderQueryService` 会调用支付宝的 `alipay.trade.query` 接口，`WxpayPayOrderQueryService` 会调用微信支付的 `order.query` 接口。查询结果被封装在 `ChannelRetMsg` 对象中，该对象包含了渠道返回的状态、订单号、错误码等信息。

`PayOrderProcessService` 本身不直接执行查询，而是作为协调者，将查询任务委派给具体的渠道实现，并根据查询结果调用相应的业务处理方法，如 `confirmSuccess` 或 `updateIngAndSuccessOrFailByCreatebyOrder`。

**Section sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L20-L97)
- [IPayOrderQueryService.java](file://sys-payment/src/main/java/com/unipay/pay/channel/IPayOrderQueryService.java#L11-L19)
- [ChannelRetMsg.java](file://sys-payment/src/main/java/com/unipay/pay/rqrs/msg/ChannelRetMsg.java#L13-L108)

## 订单状态同步机制

系统通过 `PayOrderProcessService` 中的 `updateIngAndSuccessOrFailByCreatebyOrder` 方法实现本地订单状态与渠道订单状态的同步。该方法是一个事务性操作，确保状态更新的原子性。

同步流程如下：
1.  **状态预更新**：首先调用 `payOrderService.updateInit2Ing()` 方法，将订单状态从未确定的 `STATE_INIT` 更新为 `STATE_ING`（支付中）。此操作使用乐观锁，通过 `eq(PayOrder::getState, PayOrder.STATE_INIT)` 确保只有在订单处于初始状态时才能更新，防止并发问题。
2.  **状态最终确认**：接着调用 `payOrderService.updateIng2SuccessOrFail()` 方法，根据 `ChannelRetMsg` 中的 `channelState` 来决定最终状态。如果状态为 `CONFIRM_SUCCESS`，则调用 `updateIng2Success` 将状态更新为 `STATE_SUCCESS`；如果为 `CONFIRM_FAIL`，则调用 `updateIng2Fail` 更新为 `STATE_FAIL`。

当订单状态被确认为成功时，`PayOrderProcessService` 会调用 `confirmSuccess(PayOrder payOrder)` 方法执行后续业务逻辑：
- **自动分账**：检查订单的 `divisionMode` 是否为 `DIVISION_MODE_AUTO`，如果是，则更新订单的分账状态为 `DIVISION_STATE_WAIT_TASK`，并发送 `PayOrderDivisionMQ` 消息到消息队列，触发分账任务。
- **商户通知**：调用 `PayMchNotifyService.payOrderNotify(payOrder)` 方法，生成签名后的通知URL，并创建 `MchNotifyRecord` 记录，通过消息队列异步发送给商户。

此机制确保了状态同步的可靠性和一致性，同时将耗时的后续操作（如分账、通知）异步化，提高主流程的响应速度。

```mermaid
sequenceDiagram
participant 商户 as 商户系统
participant 平台 as 统一支付平台
participant 渠道 as 支付渠道(微信/支付宝)
商户->>平台 : 发起支付查询请求
平台->>平台 : 验证商户签名
平台->>平台 : 根据mchOrderNo/payOrderId查询本地订单
平台->>平台 : 获取ifCode并获取对应的IPayOrderQueryService
平台->>渠道 : 调用渠道查询接口
渠道-->>平台 : 返回查询结果(ChannelRetMsg)
平台->>平台 : 解析ChannelRetMsg状态
alt 状态为CONFIRM_SUCCESS
平台->>平台 : 调用updateIng2Success更新本地状态
平台->>平台 : 执行confirmSuccess逻辑
平台->>平台 : 触发自动分账(可选)
平台->>平台 : 发送商户通知(异步)
else 状态为CONFIRM_FAIL
平台->>平台 : 调用updateIng2Fail更新本地状态
end
平台-->>商户 : 返回QueryPayOrderRS响应
```

**Diagram sources **
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L80-L95)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L30-L44)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L35-L83)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L47-L62)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L114-L124)

**Section sources**
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L80-L95)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L30-L44)
- [PayMchNotifyService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayMchNotifyService.java#L35-L83)

## 查询频率限制与缓存策略

本系统文档中未直接体现显式的查询频率限制（Rate Limiting）和缓存（Caching）策略的代码实现。查询操作主要通过 `QueryOrderController` 的 `queryOrder()` 方法处理，该方法直接调用 `PayOrderService` 的 `queryMchOrder` 方法从数据库读取数据。

`queryMchOrder` 方法的实现逻辑是直接通过 MyBatis-Plus 的 `getOne()` 方法，根据商户号和订单号（`payOrderId` 或 `mchOrderNo`）从数据库中查询记录。这是一个直接的数据库查询操作，没有使用如 Redis 等外部缓存来加速响应。

虽然没有在查询路径上实现缓存，但系统在其他环节使用了消息队列（MQ）来实现异步化，这间接起到了缓解数据库压力的作用。例如，当订单状态确认成功后，商户通知是通过 `mqSender.send(PayOrderMchNotifyMQ.build(...))` 异步发送的，而不是在查询或支付主流程中同步执行HTTP回调。

因此，当前的策略更侧重于数据的实时性和一致性，通过直接查询数据库来保证返回的是最新状态，而通过异步化非核心操作来保证系统的整体性能和可用性。对于高频查询场景，可能需要在应用层或网关层增加缓存机制以优化性能。

**Section sources**
- [QueryOrderController.java](file://sys-payment/src/main/java/com/unipay/pay/ctrl/payorder/QueryOrderController.java#L31-L48)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L36-L640)

## 处理查询结果不一致的代码示例

在补单或对账场景中，可能会遇到查询结果不一致的情况，例如渠道返回成功，但本地订单状态仍为“支付中”。以下代码展示了 `ChannelOrderReissueService.processPayOrder` 方法如何处理此类情况：

```java
public ChannelRetMsg processPayOrder(PayOrder payOrder){
    try {
        // 1. 根据订单的ifCode获取对应的查询服务
        IPayOrderQueryService queryService = SpringBeansUtil.getBean(payOrder.getIfCode() + "PayOrderQueryService", IPayOrderQueryService.class);
        if(queryService == null){
            log.error("{} interface not exists!", payOrder.getIfCode());
            return null;
        }

        // 2. 查询商户配置并执行渠道查询
        MchAppConfigContext mchAppConfigContext = configContextQueryService.queryMchInfoAndAppInfo(payOrder.getMchNo(), payOrder.getAppId());
        ChannelRetMsg channelRetMsg = queryService.query(payOrder, mchAppConfigContext);

        // 3. 根据渠道返回状态处理不一致
        if(channelRetMsg.getChannelState() == ChannelRetMsg.ChannelState.CONFIRM_SUCCESS) {
            // 渠道确认成功，但本地状态可能不一致，强制更新
            if (payOrderService.updateIng2Success(payOrderId, channelRetMsg.getChannelOrderId(), channelRetMsg.getChannelUserId())) {
                // 更新成功后，执行确认成功的后续业务
                payOrderProcessService.confirmSuccess(payOrder);
            }
        }else if(channelRetMsg.getChannelState() == ChannelRetMsg.ChannelState.CONFIRM_FAIL){
            // 渠道确认失败，更新本地状态
            payOrderService.updateIng2Fail(payOrderId, channelRetMsg.getChannelOrderId(), channelRetMsg.getChannelUserId(), channelRetMsg.getChannelErrCode(), channelRetMsg.getChannelErrMsg());
        }

        return channelRetMsg;
    } catch (Exception e) {
        log.error("error payOrderId = {}", payOrder.getPayOrderId(), e);
        return null;
    }
}
```

此代码的核心在于，它不信任本地的订单状态，而是以渠道返回的 `ChannelRetMsg` 为准。一旦渠道返回“明确成功”，系统就会调用 `updateIng2Success` 强制将本地订单状态更新为成功，并触发 `confirmSuccess` 流程来完成分账和通知等后续操作，从而解决了状态不一致的问题。

**Section sources**
- [ChannelOrderReissueService.java](file://sys-payment/src/main/java/com/unipay/pay/service/ChannelOrderReissueService.java#L31-L78)
- [PayOrderProcessService.java](file://sys-payment/src/main/java/com/unipay/pay/service/PayOrderProcessService.java#L30-L44)
- [PayOrderService.java](file://service/src/main/java/com/unipay/service/impl/PayOrderService.java#L47-L640)