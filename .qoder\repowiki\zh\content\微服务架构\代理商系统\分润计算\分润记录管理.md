# 分润记录管理

<cite>
**本文档引用的文件**   
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)
- [AgentProfitRecordMapper.java](file://service/src/main/java/com/unipay/service/mapper/AgentProfitRecordMapper.java)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml)
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java)
- [patch.sql](file://z-docs/sql/patch.sql)
- [init.sql](file://z-docs/sql/init.sql)
</cite>

## 目录
1. [分润记录实体数据结构](#分润记录实体数据结构)
2. [分润记录持久化机制](#分润记录持久化机制)
3. [分润记录查询接口实现](#分润记录查询接口实现)
4. [分润记录查询API示例](#分润记录查询api示例)

## 分润记录实体数据结构

`AgentProfitRecord`实体类定义了代理商分润记录的核心数据结构，用于存储分润相关的详细信息。该实体继承自`BaseModel`并实现了`Serializable`接口，通过MyBatis Plus的注解映射到数据库表`t_agent_profit_record`。

### 核心字段定义

**分润金额相关字段**
- `orderAmount`: 订单金额，单位为分，存储原始交易订单的总金额
- `mchFeeAmount`: 商户手续费，单位为分，表示商户为此订单支付的手续费
- `profitRate`: 分润比例，使用`DECIMAL(20,6)`类型存储精确的比例值
- `profitAmount`: 分润金额，单位为分，根据商户手续费和分润比例计算得出

**交易信息字段**
- `payOrderId`: 支付订单号，唯一标识一笔支付交易
- `mchNo`: 商户号，标识产生分润的商户

**代理商信息字段**
- `agentNo`: 代理商号，标识分润归属的代理商

**计算时间相关字段**
- `profitDate`: 分润日期，使用`DATE`类型存储分润计算的日期
- `createdAt`: 创建时间，记录分润记录创建的时间戳
- `updatedAt`: 更新时间，记录分润记录最后更新的时间戳

**状态管理字段**
- `state`: 分润状态，使用`TINYINT`类型存储，定义了三种状态：
  - `0`: 待结算（STATE_WAIT_SETTLE）
  - `1`: 已结算（STATE_SETTLED）
  - `2`: 已取消（STATE_CANCELLED）
- `settleTime`: 结算时间，当分润状态变为已结算时记录的具体时间

**其他辅助字段**
- `remark`: 备注信息，用于存储分润记录的附加说明
- `id`: 主键ID，自增字段，唯一标识每条分润记录

```mermaid
erDiagram
t_agent_profit_record {
BIGINT id PK
VARCHAR agent_no
VARCHAR mch_no
VARCHAR pay_order_id
BIGINT order_amount
BIGINT mch_fee_amount
DECIMAL profit_rate
BIGINT profit_amount
DATE profit_date
TINYINT state
DATETIME settle_time
VARCHAR remark
TIMESTAMP created_at
TIMESTAMP updated_at
}
```

**Diagram sources**
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)
- [patch.sql](file://z-docs/sql/patch.sql#L355-L378)

**Section sources**
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L1-L128)

## 分润记录持久化机制

分润记录的持久化机制基于MyBatis Plus框架实现，通过`AgentProfitRecordService`服务类和`AgentProfitRecordMapper`数据访问层协同工作，确保分润计算结果能够可靠地存储到数据库中。

### 数据存储流程

分润记录的创建流程始于`AgentProfitRecordService`的`createProfitRecord`方法，该方法接收代理商号、商户号、支付订单号、订单金额、商户手续费和分润比例等参数。在方法内部，首先根据商户手续费和分润比例计算分润金额：

```java
Long profitAmount = mchFeeAmount * profitRate.longValue() / 100;
```

计算完成后，创建`AgentProfitRecord`对象并设置相关属性，包括将分润状态初始化为`STATE_WAIT_SETTLE`（待结算），然后调用`save`方法将记录持久化到数据库。

### 批量操作支持

系统提供了高效的批量操作支持，通过`batchCreateProfitRecords`方法实现批量插入分润记录。该方法利用MyBatis的`<foreach>`标签生成批量插入SQL，显著提高了大量分润记录的处理效率。

对于状态更新操作，系统提供了`batchSettleProfitRecords`和`batchCancelProfitRecords`方法，通过`batchUpdateState`批量更新分润状态，并在结算时记录`settleTime`。

### 数据库表设计

数据库表`t_agent_profit_record`设计了多个索引以优化查询性能：
- `uk_agent_order`: 唯一索引，确保同一代理商对同一支付订单不会重复生成分润记录
- `idx_agent_no`: 代理商号索引，优化按代理商查询的性能
- `idx_mch_no`: 商户号索引，优化按商户查询的性能
- `idx_profit_date`: 分润日期索引，优化按时间范围查询的性能
- `idx_state`: 状态索引，优化按分润状态筛选的性能

```mermaid
flowchart TD
Start([开始分润计算]) --> Calculate["计算分润金额<br/>profitAmount = mchFeeAmount * profitRate / 100"]
Calculate --> CreateEntity["创建AgentProfitRecord实体<br/>设置agentNo, mchNo, payOrderId等字段"]
CreateEntity --> SetState["设置分润状态为待结算<br/>state = STATE_WAIT_SETTLE"]
SetState --> SetDate["设置分润日期<br/>profitDate = today"]
SetDate --> Save["调用save()方法持久化"]
Save --> End([分润记录存储完成])
```

**Diagram sources**
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L65-L83)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L66-L75)

**Section sources**
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L65-L117)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L66-L83)

## 分润记录查询接口实现

分润记录查询接口通过`ProfitRecordController`控制器暴露RESTful API，结合`AgentProfitRecordService`服务层和`AgentProfitRecordMapper`数据访问层实现完整的查询功能。

### 查询条件支持

系统支持多种查询条件的组合筛选：
- **时间范围筛选**：通过`startDate`和`endDate`参数支持按分润日期范围查询
- **代理商筛选**：自动获取当前登录用户的代理商号进行数据隔离
- **商户号筛选**：支持按商户号模糊查询
- **分润状态筛选**：支持按分润状态精确查询

### 分页查询实现

分页查询通过`selectProfitRecordPage`方法实现，该方法接收`Page`对象作为分页参数，返回`IPage<AgentProfitRecord>`类型的分页结果。在`AgentProfitRecordMapper.xml`中，对应的SQL查询语句使用动态SQL构建WHERE条件：

```xml
<select id="selectProfitRecordPage" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM t_agent_profit_record
    WHERE 1=1
    <if test="agentNo != null and agentNo != ''">
        AND agent_no = #{agentNo}
    </if>
    <if test="mchNo != null and mchNo != ''">
        AND mch_no LIKE CONCAT('%', #{mchNo}, '%')
    </if>
    <if test="state != null">
        AND state = #{state}
    </if>
    <if test="startDate != null">
        AND profit_date >= #{startDate}
    </if>
    <if test="endDate != null">
        AND profit_date &lt;= #{endDate}
    </if>
    ORDER BY created_at DESC
</select>
```

### 统计功能实现

系统提供了丰富的统计功能，`sumProfitAmount`方法返回包含多个统计指标的Map对象：
- `totalCount`: 符合条件的分润记录总数
- `totalProfitAmount`: 分润金额总和
- `totalOrderAmount`: 订单金额总和
- `totalMchFeeAmount`: 商户手续费总和

此外，还提供了`getTodayProfitStat`和`getMonthProfitStat`等便捷方法，用于快速获取当日和当月的分润统计。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant 控制器 as ProfitRecordController
participant 服务层 as AgentProfitRecordService
participant 数据访问层 as AgentProfitRecordMapper
前端->>控制器 : 发送查询请求<br/>/api/profitRecord?pageNumber=1&pageSize=10
控制器->>控制器 : 获取当前用户代理商号
控制器->>服务层 : 调用selectProfitRecordPage()
服务层->>数据访问层 : 调用selectProfitRecordPage()
数据访问层-->>服务层 : 返回分页结果
服务层-->>控制器 : 返回分页结果
控制器-->>前端 : 返回ApiPageRes格式响应
```

**Diagram sources**
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java#L48-L78)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L28-L50)

**Section sources**
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java#L48-L163)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L37-L53)
- [AgentProfitRecordMapper.xml](file://service/src/main/resources/mapper/AgentProfitRecordMapper.xml#L28-L50)

## 分润记录查询API示例

### 请求参数说明

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| iToken | String | 是 | 用户身份凭证，通过HTTP Header传递 |
| pageNumber | Integer | 否 | 分页页码，从1开始 |
| pageSize | Integer | 否 | 分页条数，-1表示查询全部数据 |
| mchNo | String | 否 | 商户号，支持模糊查询 |
| state | Byte | 否 | 分润状态：0-待结算，1-已结算，2-已取消 |
| startDate | Date | 否 | 开始日期，格式yyyy-MM-dd |
| endDate | Date | 否 | 结束日期，格式yyyy-MM-dd |

### 响应格式

成功响应（HTTP 200）：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 12345,
        "agentNo": "AGT2024001",
        "mchNo": "MCH2024001",
        "payOrderId": "PO202401010001",
        "orderAmount": 10000,
        "mchFeeAmount": 100,
        "profitRate": 50.000000,
        "profitAmount": 50,
        "profitDate": "2024-01-01",
        "state": 0,
        "settleTime": null,
        "remark": "系统自动计算",
        "createdAt": "2024-01-01T10:00:00.000",
        "updatedAt": "2024-01-01T10:00:00.000"
      }
    ],
    "pageNumber": 1,
    "pageSize": 10
  }
}
```

统计接口成功响应：
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "totalCount": 50,
    "totalProfitAmount": 2500,
    "totalOrderAmount": 500000,
    "totalMchFeeAmount": 5000
  }
}
```

### 错误处理

| 错误码 | 错误信息 | 说明 |
|-------|--------|------|
| SYS_OPERATION_FAIL_SELETE | 该分润记录不存在 | 请求的分润记录ID不存在 |
| SYS_OPERATION_FAIL_SELETE | 无权限查看该分润记录 | 当前用户无权访问指定的分润记录 |
| 401 | Unauthorized | iToken无效或过期 |
| 403 | Forbidden | 用户无权访问该接口 |

### 使用示例

查询特定时间段内的分润记录：
```
GET /api/profitRecord?startDate=2024-01-01&endDate=2024-01-31&state=0
Headers: {
  "iToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

获取本月分润统计：
```
GET /api/profitRecord/monthStatistics
Headers: {
  "iToken": "eyJhbGciOiJIUzI1NiIs..."
}
```

**Section sources**
- [ProfitRecordController.java](file://sys-agent/src/main/java/com/unipay/agent/ctrl/profit/ProfitRecordController.java#L48-L163)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)
- [ApiRes.java](file://core/src/main/java/com/unipay/core/model/ApiRes.java)