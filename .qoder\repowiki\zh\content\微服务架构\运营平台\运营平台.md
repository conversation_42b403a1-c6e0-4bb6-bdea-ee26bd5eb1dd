# 运营平台

<cite>
**本文档引用的文件**  
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java)
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java)
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java)
- [SysUserAuth.java](file://core/src/main/java/com/unipay/core/entity/SysUserAuth.java)
- [SysUserRoleRela.java](file://core/src/main/java/com/unipay/core/entity/SysUserRoleRela.java)
- [SysRoleEntRela.java](file://core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java)
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java)
- [MchInfo.java](file://core/src/main/java/com/unipay/core/entity/MchInfo.java)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java)
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java)
- [SysUserAuthService.java](file://service/src/main/java/com/unipay/service/impl/SysUserAuthService.java)
- [SysUserRoleRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysUserRoleRelaService.java)
- [SysRoleEntRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心功能概述](#核心功能概述)
3. [系统配置管理](#系统配置管理)
4. [用户与权限管理](#用户与权限管理)
5. [全局监控与消息机制](#全局监控与消息机制)
6. [与代理商及商户系统的数据交互](#与代理商及商户系统的数据交互)
7. [管理员操作指南](#管理员操作指南)
8. [架构与流程图](#架构与流程图)
9. [结论](#结论)

## 简介

运营平台是整个统一支付系统（Uni-Pay）的管理中枢，承担着用户管理、角色权限控制、系统配置管理以及全局监控的核心职责。它为系统管理员提供了一个集中化的控制界面，用于配置全局参数、管理用户权限、监控系统状态，并确保各微服务模块之间的协调运行。本平台通过高效的消息队列机制，实现了配置的动态刷新，保证了系统的灵活性和高可用性。

## 核心功能概述

运营平台的核心功能包括：
- **用户管理**：创建、编辑和管理所有系统用户，包括运营平台、商户系统和代理商系统的操作员。
- **角色权限控制**：定义角色并分配权限，实现基于角色的访问控制（RBAC），确保最小权限原则。
- **系统配置管理**：集中管理所有全局系统参数，如各平台的访问地址、支付网关地址等。
- **全局监控**：提供系统运行状态的监控视图，便于管理员及时发现和处理问题。
- **消息广播**：通过消息队列将配置变更等关键事件广播至所有相关微服务，实现系统的动态响应。

## 系统配置管理

### 配置信息的存储与查询

系统配置信息存储在数据库的 `t_sys_config` 表中，对应的实体类为 `SysConfig`。每个配置项包含 `configKey`（配置键）、`configVal`（配置值）、`groupKey`（分组键）等字段。配置信息按功能分组，例如 `applicationConfig` 分组包含了所有应用级别的配置。

`SysConfigController` 提供了 RESTful API 来查询和修改配置。`getConfigs` 方法允许管理员根据 `groupKey` 查询特定分组下的所有配置项。

```mermaid
flowchart TD
A[管理员请求] --> B{SysConfigController}
B --> C[调用SysConfigService]
C --> D[查询t_sys_config表]
D --> E[返回配置列表]
E --> F[响应给前端]
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L53-L69)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L69-L75)

### 配置的动态刷新机制

当管理员在运营平台修改系统配置后，系统会通过消息队列（MQ）将变更广播到所有相关的微服务实例，实现配置的动态刷新，而无需重启服务。

1.  **配置修改**：管理员通过 `SysConfigController` 的 `update` 方法提交新的配置值。
2.  **更新数据库**：`SysConfigService` 将新的配置值持久化到数据库。
3.  **发送MQ消息**：`SysConfigController` 调用 `updateSysConfigMQ` 方法，通过 `IMQSender` 发送一条 `ResetAppConfigMQ` 消息。
4.  **接收并处理**：所有订阅了该消息的微服务（如 `sys-manager`, `sys-merchant`, `sys-payment`, `sys-agent`）中的 `ResetAppConfigMQReceiver` 都会接收到此消息。
5.  **刷新本地缓存**：接收方调用 `SysConfigService.initDBConfig` 方法，重新从数据库加载最新的配置并更新本地缓存。

```mermaid
sequenceDiagram
participant Admin as 管理员
participant SysConfigController as SysConfigController
participant MQ as 消息队列
participant Manager as 运营平台
participant Merchant as 商户系统
participant Payment as 支付网关
participant Agent as 代理商系统
Admin->>SysConfigController : 发起配置更新请求
SysConfigController->>SysConfigService : 更新数据库
SysConfigController->>MQ : 发送ResetAppConfigMQ消息
MQ->>Manager : 广播消息
MQ->>Merchant : 广播消息
MQ->>Payment : 广播消息
MQ->>Agent : 广播消息
Manager->>SysConfigService : initDBConfig()
Merchant->>SysConfigService : initDBConfig()
Payment->>SysConfigService : initDBConfig()
Agent->>SysConfigService : initDBConfig()
SysConfigService-->>SysConfigController : 配置刷新完成
SysConfigController-->>Admin : 返回更新成功
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L76-L101)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L22-L28)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java#L22-L28)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L22-L28)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java#L22-L28)

**Section sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L38-L109)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L39-L49)

## 用户与权限管理

### 用户与角色模型

运营平台采用标准的RBAC（基于角色的访问控制）模型来管理用户权限。

- **SysUser**：代表系统用户，包含用户的基本信息（如用户名、手机号）和所属系统（`MGR`-运营平台）。
- **SysRole**：代表角色，每个角色有唯一的 `roleId` 和 `roleName`。
- **SysUserRoleRela**：用户与角色的关联表，一个用户可以拥有多个角色。
- **SysEntitlement**：代表权限，定义了具体的菜单、按钮或操作。
- **SysRoleEntRela**：角色与权限的关联表，一个角色可以被赋予多个权限。

```mermaid
erDiagram
SysUser ||--o{ SysUserRoleRela : "拥有"
SysRole ||--o{ SysUserRoleRela : "被分配"
SysRole ||--o{ SysRoleEntRela : "包含"
SysEntitlement ||--o{ SysRoleEntRela : "被赋予"
SysUser {
string loginUsername
string realname
string telphone
byte isAdmin
}
SysRole {
string roleId
string roleName
}
SysUserRoleRela {
long userId
string roleId
}
SysEntitlement {
string entId
string entName
string entType
}
SysRoleEntRela {
string roleId
string entId
}
```

**Diagram sources**
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java#L23-L117)
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java#L22-L68)
- [SysUserRoleRela.java](file://core/src/main/java/com/unipay/core/entity/SysUserRoleRela.java#L21-L49)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java#L22-L116)
- [SysRoleEntRela.java](file://core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java#L21-L49)

### 权限分配与验证

管理员可以通过管理界面为用户分配角色。`SysRoleEntRelaService` 提供了 `resetRela` 方法，用于重置某个角色的所有权限，从而实现权限的批量分配。

当用户登录或访问受保护的资源时，系统会进行权限验证。`SysRoleEntRelaService` 的 `selectEntIdsByUserId` 方法会根据用户的 `userId` 和 `isAdmin` 状态，查询出该用户拥有的所有权限ID列表。如果用户是超管（`isAdmin=1`），则自动拥有该系统下的所有可用权限。

**Section sources**
- [SysUser.java](file://core/src/main/java/com/unipay/core/entity/SysUser.java#L23-L117)
- [SysRole.java](file://core/src/main/java/com/unipay/core/entity/SysRole.java#L22-L68)
- [SysUserRoleRela.java](file://core/src/main/java/com/unipay/core/entity/SysUserRoleRela.java#L21-L49)
- [SysEntitlement.java](file://core/src/main/java/com/unipay/core/entity/SysEntitlement.java#L22-L116)
- [SysRoleEntRela.java](file://core/src/main/java/com/unipay/core/entity/SysRoleEntRela.java#L21-L49)
- [SysRoleEntRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java#L29-L44)
- [SysRoleEntRelaService.java](file://service/src/main/java/com/unipay/service/impl/SysRoleEntRelaService.java#L48-L61)

## 全局监控与消息机制

运营平台作为系统的监控中心，其核心在于利用消息队列（MQ）实现服务间的松耦合通信。`ResetAppConfigMQ` 消息采用广播模式（`BROADCAST`），确保所有在线的微服务实例都能接收到配置变更通知。这种机制不仅用于配置刷新，也为未来的扩展（如日志聚合、告警通知）提供了基础。

## 与代理商及商户系统的数据交互

### 与代理商系统的交互

运营平台与代理商系统（`sys-agent`）通过共享数据库表进行数据交互。

- **AgentInfo**：存储代理商的基本信息，如代理商号、名称、层级和分润比例。
- **AgentMchRelation**：记录代理商与商户的关联关系。当一个商户被创建并分配给某个代理商时，会在该表中创建一条记录。`AgentMchRelationService` 提供了创建、查询和管理这些关系的方法。
- **AgentProfitRecord**：当一笔交易成功后，系统会根据 `AgentMchRelation` 中的分润比例，计算出代理商应得的分润金额，并在 `AgentProfitRecord` 表中生成一条分润记录。`AgentProfitRecordService` 负责管理这些记录，支持分页查询、统计和批量结算。

```mermaid
flowchart LR
A[支付成功] --> B{检查商户归属}
B --> |商户属于代理商| C[计算分润金额]
C --> D[创建AgentProfitRecord]
D --> E[记录到t_agent_profit_record表]
E --> F[待结算]
```

**Diagram sources**
- [AgentInfo.java](file://core/src/main/java/com/unipay/core/entity/AgentInfo.java#L25-L201)
- [AgentMchRelation.java](file://core/src/main/java/com/unipay/core/entity/AgentMchRelation.java#L25-L107)
- [AgentProfitRecord.java](file://core/src/main/java/com/unipay/core/entity/AgentProfitRecord.java#L25-L128)
- [AgentMchRelationService.java](file://service/src/main/java/com/unipay/service/impl/AgentMchRelationService.java#L25-L311)
- [AgentProfitRecordService.java](file://service/src/main/java/com/unipay/service/impl/AgentProfitRecordService.java#L24-L167)

### 与商户系统的交互

运营平台与商户系统（`sys-merchant`）的交互主要体现在用户和配置管理上。

- **用户同步**：虽然 `SysUser` 和 `SysUserAuth` 表中 `sysType` 字段可以区分用户属于哪个系统，但运营平台通常不直接管理商户系统的操作员。商户的操作员由商户管理员在商户系统中自行创建和管理。
- **配置共享**：`DBApplicationConfig` 中的 `mchSiteUrl` 配置项定义了商户系统的访问地址。当运营平台需要跳转到商户系统或生成相关链接时，会使用此配置。

## 管理员操作指南

### 用户权限分配

1.  登录运营平台。
2.  导航至“用户管理”模块。
3.  选择一个用户，点击“编辑”或“分配角色”。
4.  在弹出的对话框中，从可用角色列表中选择一个或多个角色。
5.  保存更改。系统会自动更新 `SysUserRoleRela` 表。

### 系统监控

1.  登录运营平台。
2.  查看“仪表盘”或“系统监控”页面。
3.  监控关键指标，如服务健康状态、数据库连接数、消息队列积压情况等。
4.  如发现异常，可查看相关日志或通知进行排查。

## 架构与流程图

```mermaid
graph TD
subgraph "前端"
UI[运营平台Web UI]
end
subgraph "运营平台 (sys-manager)"
SC[SysConfigController]
SS[SysConfigService]
MQS[IMQSender]
UAC[SysUserAuthService]
RAC[SysRoleEntRelaService]
end
subgraph "消息队列"
MQ[ResetAppConfigMQ]
end
subgraph "其他微服务"
MGR[运营平台]
MCH[商户系统]
PAY[支付网关]
AGT[代理商系统]
end
UI --> SC
SC --> SS
SS --> DB[(数据库)]
SC --> MQS --> MQ
MQ --> MGR
MQ --> MCH
MQ --> PAY
MQ --> AGT
MGR --> SS
MCH --> SS
PAY --> SS
AGT --> SS
```

**Diagram sources**
- [SysConfigController.java](file://sys-manager/src/main/java/com/unipay/mgr/ctrl/config/SysConfigController.java#L38-L109)
- [SysConfigService.java](file://service/src/main/java/com/unipay/service/impl/SysConfigService.java#L24-L92)
- [ResetAppConfigMQ.java](file://components/components-mq/src/main/java/com/unipay/components/mq/model/ResetAppConfigMQ.java#L16-L67)
- [ResetAppConfigMQReceiver.java](file://sys-manager/src/main/java/com/unipay/mgr/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-merchant/src/main/java/com/unipay/mch/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-payment/src/main/java/com/unipay/pay/mq/ResetAppConfigMQReceiver.java#L15-L29)
- [ResetAppConfigMQReceiver.java](file://sys-agent/src/main/java/com/unipay/agent/mq/ResetAppConfigMQReceiver.java#L15-L29)

## 结论

运营平台作为统一支付系统的核心管理枢纽，通过精细化的用户权限控制、集中的系统配置管理和高效的消息广播机制，实现了对整个系统的有效管控。它不仅为管理员提供了强大的管理工具，还通过与代理商系统和商户系统的紧密数据交互，支撑了复杂的分润和业务协同场景。其基于微服务和消息队列的架构设计，确保了系统的可扩展性、灵活性和高可用性。