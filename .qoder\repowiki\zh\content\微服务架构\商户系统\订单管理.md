
# 订单管理

<cite>
**Referenced Files in This Document**   
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java)
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java)
- [RefundOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\RefundOrderController.java)
- [PayOrder.java](file://core\src\main\java\com\unipay\core\entity\PayOrder.java)
- [RefundOrder.java](file://core\src\main\java\com\unipay\core\entity\RefundOrder.java)
- [PayOrderService.java](file://service\src\main\java\com\unipay\service\impl\PayOrderService.java)
- [RefundOrderService.java](file://service\src\main\java\com\unipay\service\impl\RefundOrderService.java)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java)
- [MchNotifyRecord.java](file://core\src\main\java\com\unipay\core\entity\MchNotifyRecord.java)
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java)
</cite>

## 目录
1. [订单管理](#订单管理)
2. [支付结果异步通知处理](#支付结果异步通知处理)
3. [订单查询API](#订单查询api)
4. [消息队列与最终一致性](#消息队列与最终一致性)
5. [订单状态机设计](#订单状态机设计)
6. [通知丢失与重复处理](#通知丢失与重复处理)

## 支付结果异步通知处理

商户系统通过 `PayOrderMchNotifyMQReceiver` 接收来自支付网关的支付结果异步通知，该组件实现了消息队列的接收接口，负责处理支付成功或失败后的商户通知逻辑。

当支付订单状态变为终态（如支付成功或支付失败）时，系统会调用 `PayMchNotifyService.payOrderNotify()` 方法，该方法首先检查订单的异步通知地址（`notifyUrl`）是否为空。如果地址有效，则创建一条 `MchNotifyRecord` 记录，该记录包含订单ID、商户号、应用ID、通知地址等信息，并将状态设置为“通知中”（`STATE_ING`）。为防止重复通知，系统会先检查是否已存在针对该订单的通知记录，若存在则不再发送。

通知记录创建成功后，系统会将其ID封装成 `PayOrderMchNotifyMQ.MsgPayload` 消息体，并通过 `IMQSender` 发送到名为 `QUEUE_PAY_ORDER_MCH_NOTIFY` 的消息队列中。此过程实现了业务逻辑与通知发送的解耦。

`PayOrderMchNotifyMQReceiver` 作为消息消费者，会监听该队列。当接收到消息时，它会根据 `notifyId` 从数据库中查询对应的 `MchNotifyRecord`。如果记录不存在或状态不是“通知中”，则忽略该消息。系统会检查当前通知次数是否已达到上限（默认6次），若已达到则停止发送。

随后，系统会使用 `HttpUtil` 向商户配置的 `notifyUrl` 发起POST请求，将支付结果数据（如订单号、金额、状态等）以JSON格式发送。请求体中包含一个名为 `sign` 的签名字段，用于商户验证数据来源的合法性。如果HTTP请求成功且商户返回“SUCCESS”响应，则将通知记录状态更新为“通知成功”（`STATE_SUCCESS`）。

如果通知失败或未收到“SUCCESS”响应，系统会根据当前通知次数决定后续操作。若未达到最大次数，系统会将通知记录状态保持为“通知中”，并再次通过 `IMQSender.send()` 方法将消息发送到队列，但这次会设置一个延迟时间（延迟时间随通知次数递增，分别为0、30、60、90、120、150秒），实现指数退避重试机制。

**Section sources**
- [PayOrderMchNotifyMQReceiver.java](file://sys-payment\src\main\java\com\unipay\pay\mq\PayOrderMchNotifyMQReceiver.java#L23-L98)
- [PayMchNotifyService.java](file://sys-payment\src\main\java\com\unipay\pay\service\PayMchNotifyService.java#L35-L83)
- [PayOrderMchNotifyMQ.java](file://components\components-mq\src\main\java\com\unipay\components\mq\model\PayOrderMchNotifyMQ.java#L16-L68)
- [IMQSender.java](file://components\components-mq\src\main\java\com\unipay\components\mq\vender\IMQSender.java#L9-L17)
- [MchNotifyRecord.java](file://core\src\main\java\com\unipay\core\entity\MchNotifyRecord.java#L24-L140)

## 订单查询API

系统为商户提供了标准的RESTful API，用于查询 `PayOrder`（支付订单）和 `RefundOrder`（退款订单）的最新状态。

### 支付订单查询API

`PayOrderController` 提供了两个核心接口：
1.  **列表查询 (`GET /api/payOrder`)**：商户可以通过此接口分页查询其名下的所有支付订单。请求支持多种筛选条件，包括商户订单号（`mchOrderNo`）、支付订单号（`payOrderId`）、支付状态（`state`）、创建时间范围等。系统会根据商户的权限自动过滤数据，确保商户只能查询到属于自己的订单。查询结果会包含订单的详细信息，并通过 `addExt("wayName", ...)` 方法将支付方式代码转换为可读的支付方式名称。
2.  **详情查询 (`GET /api/payOrder/{payOrderId}`)**：商户可以通过支付订单号精确查询单个订单的详细信息。在返回数据前，系统会校验该订单是否属于当前商户，以保证数据安全。

### 退款订单查询API

`RefundOrderController` 提供了类似的接口用于退款订单的查询：
1.  **列表查询 (`GET /api/refundOrder`)**：功能与支付订单列表查询类似，支持按退款订单号、关联的支付订单号、退款状态等条件进行筛选。
2.  **详情查询 (`GET /api/refundOrder/{refundOrderId}`)**：通过退款订单号查询单个退款订单的详情，并进行商户权限校验。

这些API的返回结果均封装在 `ApiRes` 或 `ApiPageRes` 对象中，包含统一的响应码（`code`）、消息（`msg`）和数据体（`data`），便于商户系统进行统一处理。

**Section sources**
- [PayOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\PayOrderController.java#L46-L192)
- [RefundOrderController.java](file://sys-merchant\src\main\java\com\unipay\mch\ctrl\order\RefundOrderController.java#L30-L89)
- [ApiRes.java](file://core\src\main\java\com\unipay\core\model\ApiRes.java#L20-L100)
- [ApiPageRes.java](file://core\src\main\java\com\unipay\core\model\ApiPageRes.java#L14-L63)

## 消息队列与最终一致性

消息队列在本系统中扮演着保证最终一致性的关键角色。由于网络的不可靠性，直接从支付网关到商户系统的HTTP通知可能会失败。为了确保通知的可靠送达，系统采用了“先持久化，后异步通知”的策略。

当支付订单状态变更时，系统首先将通知意图（即 `MchNotifyRecord`）持久化到数据库，这保证了通知